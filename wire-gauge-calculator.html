<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wire Gauge Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free wire gauge calculator for AWG, current capacity, resistance, and wire sizing. Essential tool for electrical engineers and technicians.">
    <style>
        .wire-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .wire-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .wire-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .wire-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .wire-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .wire-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .wire-table th, .wire-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .wire-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        @media (max-width: 768px) {
            .wire-calc {
                padding: 15px;
            }
            
            .wire-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
            
            .wire-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="wire-calc">
        <header>
            <h1>Wire Gauge Calculator</h1>
            <p class="intro">Calculate wire gauge (AWG), current capacity, resistance, and voltage drop for electrical wiring applications. Essential tool for electrical design and safety.</p>
        </header>

        <div class="calculator">
            <div class="calc-section">
                <h3>Wire Specifications</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="awgGauge">AWG Gauge</label>
                        <select id="awgGauge">
                            <option value="">Select AWG</option>
                            <option value="14">14 AWG</option>
                            <option value="12">12 AWG</option>
                            <option value="10">10 AWG</option>
                            <option value="8">8 AWG</option>
                            <option value="6">6 AWG</option>
                            <option value="4">4 AWG</option>
                            <option value="2">2 AWG</option>
                            <option value="1">1 AWG</option>
                            <option value="0">1/0 AWG</option>
                            <option value="-1">2/0 AWG</option>
                            <option value="-2">3/0 AWG</option>
                            <option value="-3">4/0 AWG</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label for="wireLength">Wire Length (feet)</label>
                        <input type="number" id="wireLength" step="any" placeholder="Enter length">
                    </div>
                    <div class="input-field">
                        <label for="current">Current (Amperes)</label>
                        <input type="number" id="current" step="any" placeholder="Enter current">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Circuit Parameters</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="voltage">System Voltage (V)</label>
                        <input type="number" id="voltage" step="any" value="120" placeholder="Enter voltage">
                    </div>
                    <div class="input-field">
                        <label for="maxVoltageDrop">Max Voltage Drop (%)</label>
                        <input type="number" id="maxVoltageDrop" step="any" value="3" placeholder="Enter max drop %">
                    </div>
                    <div class="input-field">
                        <label for="temperature">Temperature (°C)</label>
                        <input type="number" id="temperature" step="any" value="75" placeholder="Enter temperature">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>AWG Wire Reference Table</h2>
            <table class="wire-table">
                <thead>
                    <tr>
                        <th>AWG</th>
                        <th>Diameter (mm)</th>
                        <th>Area (mm²)</th>
                        <th>Resistance (Ω/km)</th>
                        <th>Current Capacity (A)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>14</td><td>1.63</td><td>2.08</td><td>8.29</td><td>15</td></tr>
                    <tr><td>12</td><td>2.05</td><td>3.31</td><td>5.21</td><td>20</td></tr>
                    <tr><td>10</td><td>2.59</td><td>5.26</td><td>3.28</td><td>30</td></tr>
                    <tr><td>8</td><td>3.26</td><td>8.37</td><td>2.06</td><td>40</td></tr>
                    <tr><td>6</td><td>4.11</td><td>13.3</td><td>1.30</td><td>55</td></tr>
                    <tr><td>4</td><td>5.19</td><td>21.2</td><td>0.815</td><td>70</td></tr>
                </tbody>
            </table>
            
            <div class="highlight-box">
                <h3>Important Notes</h3>
                <p><strong>Current Capacity:</strong> Based on 75°C insulation rating in conduit</p>
                <p><strong>Voltage Drop:</strong> Calculated for copper wire at 75°C</p>
                <p><strong>Safety:</strong> Always consult local electrical codes and regulations</p>
            </div>
        </div>
    </div>

    <script>
        const wireData = {
            14: { diameter: 1.63, area: 2.08, resistance: 8.29, capacity: 15 },
            12: { diameter: 2.05, area: 3.31, resistance: 5.21, capacity: 20 },
            10: { diameter: 2.59, area: 5.26, resistance: 3.28, capacity: 30 },
            8: { diameter: 3.26, area: 8.37, resistance: 2.06, capacity: 40 },
            6: { diameter: 4.11, area: 13.3, resistance: 1.30, capacity: 55 },
            4: { diameter: 5.19, area: 21.2, resistance: 0.815, capacity: 70 },
            2: { diameter: 6.54, area: 33.6, resistance: 0.513, capacity: 95 },
            1: { diameter: 7.35, area: 42.4, resistance: 0.407, capacity: 110 },
            0: { diameter: 8.25, area: 53.5, resistance: 0.323, capacity: 125 },
            '-1': { diameter: 9.27, area: 67.4, resistance: 0.256, capacity: 145 },
            '-2': { diameter: 10.4, area: 85.0, resistance: 0.203, capacity: 165 },
            '-3': { diameter: 11.7, area: 107, resistance: 0.161, capacity: 195 }
        };
        
        function calculate() {
            const awg = document.getElementById('awgGauge').value;
            const length = parseFloat(document.getElementById('wireLength').value);
            const current = parseFloat(document.getElementById('current').value);
            const voltage = parseFloat(document.getElementById('voltage').value) || 120;
            const maxDrop = parseFloat(document.getElementById('maxVoltageDrop').value) || 3;
            const temperature = parseFloat(document.getElementById('temperature').value) || 75;
            
            if (!awg || !wireData[awg]) {
                alert('Please select a valid AWG gauge');
                return;
            }
            
            const wire = wireData[awg];
            let results = [];
            
            // Wire specifications
            results.push(`<div class="result-item"><strong>Wire Gauge:</strong> ${awg} AWG</div>`);
            results.push(`<div class="result-item"><strong>Wire Diameter:</strong> ${wire.diameter} mm</div>`);
            results.push(`<div class="result-item"><strong>Cross-sectional Area:</strong> ${wire.area} mm²</div>`);
            results.push(`<div class="result-item"><strong>Current Capacity:</strong> ${wire.capacity} A</div>`);
            
            // Temperature derating
            const tempFactor = temperature > 75 ? 0.82 : 1.0;
            const deratedCapacity = wire.capacity * tempFactor;
            if (temperature > 75) {
                results.push(`<div class="result-item"><strong>Derated Capacity (${temperature}°C):</strong> ${deratedCapacity.toFixed(1)} A</div>`);
            }
            
            // Current check
            if (current) {
                const safetyMargin = (deratedCapacity - current) / deratedCapacity * 100;
                results.push(`<div class="result-item"><strong>Safety Margin:</strong> ${safetyMargin.toFixed(1)}%</div>`);
                
                if (current > deratedCapacity) {
                    results.push(`<div class="result-item" style="color: red;"><strong>Warning:</strong> Current exceeds wire capacity!</div>`);
                }
            }
            
            // Voltage drop calculation
            if (length && current) {
                const resistance = wire.resistance * (length * 0.3048 / 1000) * 2; // Round trip in km
                const voltageDrop = current * resistance;
                const dropPercentage = (voltageDrop / voltage) * 100;
                
                results.push(`<div class="result-item"><strong>Voltage Drop:</strong> ${voltageDrop.toFixed(2)} V (${dropPercentage.toFixed(1)}%)</div>`);
                
                if (dropPercentage > maxDrop) {
                    results.push(`<div class="result-item" style="color: red;"><strong>Warning:</strong> Voltage drop exceeds ${maxDrop}%!</div>`);
                }
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function reset() {
            document.getElementById('awgGauge').selectedIndex = 0;
            document.getElementById('wireLength').value = '';
            document.getElementById('current').value = '';
            document.getElementById('voltage').value = '120';
            document.getElementById('maxVoltageDrop').value = '3';
            document.getElementById('temperature').value = '75';
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
