<!-- Wire Gauge Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .wire-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .wire-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }

    /* Header styles */
    .wire-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .wire-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }

    .wire-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }

    /* Calculator section */
    .wire-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .wire-calc .input-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .wire-calc .input-field {
        display: flex;
        flex-direction: column;
    }

    .wire-calc .input-field label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }

    .wire-calc .input-with-unit {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .wire-calc .input-with-unit input {
        flex: 3;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
    }

    .wire-calc .input-with-unit input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .wire-calc .input-with-unit select {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        min-width: 60px;
        max-width: 80px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
        cursor: pointer;
    }

    .wire-calc .input-with-unit select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .wire-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .wire-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }

    .wire-calc .calculate {
        background-color: #3498db;
        color: white;
    }

    .wire-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .wire-calc .reset {
        background-color: #e74c3c;
        color: white;
    }

    .wire-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Results section */
    .wire-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }

    .wire-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .wire-calc .results ul {
        list-style: none;
    }

    .wire-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }

    /* Wire table */
    .wire-calc .wire-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .wire-calc .wire-table th,
    .wire-calc .wire-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .wire-calc .wire-table th {
        background-color: #f7fafc;
        color: #2c3e50;
        font-weight: 600;
    }

    /* Content section */
    .wire-calc .content-section {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-top: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .wire-calc .content-section h2 {
        color: #2c3e50;
        margin-bottom: 18px;
        font-size: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }

    .wire-calc .content-section h3 {
        color: #2c3e50;
        margin: 20px 0 10px;
        font-size: 1.25rem;
    }

    .wire-calc .content-section p {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.7;
    }

    .wire-calc .content-section strong {
        color: #2c3e50;
        font-weight: 600;
    }

    .wire-calc .content-section ol,
    .wire-calc .content-section ul {
        margin-bottom: 20px;
        padding-left: 25px;
    }

    .wire-calc .content-section li {
        margin-bottom: 8px;
        color: #555;
        line-height: 1.7;
    }

    /* Highlight box for important content */
    .wire-calc .highlight-box {
        background-color: #edf7ff;
        border-left: 4px solid #3498db;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }

    .wire-calc .highlight-box h3 {
        margin-top: 0;
        color: #2c3e50;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .wire-calc {
            padding: 15px;
        }

        .wire-calc h1 {
            font-size: 1.7rem;
        }

        .wire-calc .input-group {
            grid-template-columns: 1fr;
        }

        .wire-calc .input-with-unit {
            flex-direction: column;
            gap: 8px;
        }

        .wire-calc .input-with-unit select {
            max-width: none;
        }

        .wire-calc .button-group {
            flex-direction: column;
        }

        .wire-calc .btn-group {
            flex-direction: column;
            gap: 10px;
        }

        .wire-calc .btn {
            width: 100%;
        }

        .wire-calc .content-section {
            padding: 20px;
        }

        .wire-calc .highlight-box {
            padding: 15px;
        }

        .wire-calc .wire-table {
            font-size: 0.9rem;
            overflow-x: auto;
            display: block;
            white-space: nowrap;
        }

        .wire-calc .wire-table th,
        .wire-calc .wire-table td {
            padding: 8px 6px;
            font-size: 0.85rem;
        }
    }

    @media (max-width: 480px) {
        .wire-calc {
            padding: 12px;
        }

        .wire-calc h1 {
            font-size: 1.5rem;
        }

        .wire-calc .calc-section {
            padding: 15px;
        }

        .wire-calc .input-field label {
            font-size: 0.9rem;
        }

        .wire-calc .input-field input,
        .wire-calc .input-field select {
            padding: 10px 12px;
            font-size: 1rem;
        }

        .wire-calc .wire-table {
            font-size: 0.8rem;
        }

        .wire-calc .wire-table th,
        .wire-calc .wire-table td {
            padding: 6px 4px;
            font-size: 0.75rem;
        }

        .wire-calc .content-section h2 {
            font-size: 1.3rem;
        }

        .wire-calc .content-section h3 {
            font-size: 1.1rem;
        }

        .wire-calc .content-section ul {
            padding-left: 20px;
        }
    }
</style>

<div class="wire-calc">
    <header>
        <h1>Wire Gauge Calculator</h1>
        <p class="intro">Calculate AWG wire gauge, current capacity, resistance, and voltage drop for electrical wiring projects. Essential tool for safe electrical installations.</p>
    </header>

    <div class="calculator">
        <div class="input-group">
            <div class="input-field">
                <label for="current">Current Load</label>
                <div class="input-with-unit">
                    <input type="number" id="current" placeholder="Enter current" step="0.1" min="0">
                    <select id="current-unit">
                        <option value="A">A</option>
                        <option value="mA">mA</option>
                    </select>
                </div>
            </div>

            <div class="input-field">
                <label for="length">Wire Length</label>
                <div class="input-with-unit">
                    <input type="number" id="length" placeholder="Enter length" step="0.1" min="0">
                    <select id="length-unit">
                        <option value="ft">ft</option>
                        <option value="m">m</option>
                        <option value="in">in</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="input-group">
            <div class="input-field">
                <label for="voltage">System Voltage</label>
                <div class="input-with-unit">
                    <input type="number" id="voltage" placeholder="Enter voltage" step="0.1" min="0">
                    <select id="voltage-unit">
                        <option value="V">V</option>
                        <option value="kV">kV</option>
                    </select>
                </div>
            </div>

            <div class="input-field">
                <label for="voltage-drop">Max Voltage Drop (%)</label>
                <div class="input-with-unit">
                    <input type="number" id="voltage-drop" placeholder="3" value="3" step="0.1" min="0" max="10">
                    <select disabled>
                        <option>%</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button class="btn calculate" onclick="calculateWireGauge()">Calculate</button>
            <button class="btn reset" onclick="resetCalculator()">Reset</button>
        </div>

        <div class="results" id="results" style="display: none;">
            <h3>Wire Sizing Results</h3>
            <ul id="results-list">
                <!-- Results will be displayed here -->
            </ul>
        </div>
    </div>
</div>

<script>
    // AWG wire data with accurate specifications
    const wireData = {
        30: { diameter: 0.255, area: 0.0507, resistance: 338.6, ampacity: 0.86 },
        28: { diameter: 0.321, area: 0.0804, resistance: 213.9, ampacity: 1.4 },
        26: { diameter: 0.405, area: 0.128, resistance: 134.5, ampacity: 2.2 },
        24: { diameter: 0.511, area: 0.205, resistance: 84.22, ampacity: 3.5 },
        22: { diameter: 0.644, area: 0.326, resistance: 52.96, ampacity: 5 },
        20: { diameter: 0.812, area: 0.518, resistance: 33.31, ampacity: 7.5 },
        18: { diameter: 1.024, area: 0.823, resistance: 20.95, ampacity: 10 },
        16: { diameter: 1.291, area: 1.31, resistance: 13.17, ampacity: 13 },
        14: { diameter: 1.628, area: 2.08, resistance: 8.286, ampacity: 15 },
        12: { diameter: 2.053, area: 3.31, resistance: 5.211, ampacity: 20 },
        10: { diameter: 2.588, area: 5.26, resistance: 3.277, ampacity: 30 },
        8: { diameter: 3.264, area: 8.37, resistance: 2.061, ampacity: 40 },
        6: { diameter: 4.115, area: 13.3, resistance: 1.296, ampacity: 55 },
        4: { diameter: 5.189, area: 21.2, resistance: 0.8152, ampacity: 70 },
        3: { diameter: 5.827, area: 26.7, resistance: 0.6465, ampacity: 85 },
        2: { diameter: 6.544, area: 33.6, resistance: 0.5127, ampacity: 95 },
        1: { diameter: 7.348, area: 42.4, resistance: 0.4066, ampacity: 110 },
        0: { diameter: 8.251, area: 53.5, resistance: 0.3225, ampacity: 125 },
        '00': { diameter: 9.266, area: 67.4, resistance: 0.2558, ampacity: 145 },
        '000': { diameter: 10.40, area: 85.0, resistance: 0.2028, ampacity: 165 },
        '0000': { diameter: 11.68, area: 107, resistance: 0.1608, ampacity: 195 }
    };

    function calculateWireGauge() {
        const current = parseFloat(document.getElementById('current').value);
        const currentUnit = document.getElementById('current-unit').value;
        const length = parseFloat(document.getElementById('length').value);
        const lengthUnit = document.getElementById('length-unit').value;
        const voltage = parseFloat(document.getElementById('voltage').value);
        const voltageUnit = document.getElementById('voltage-unit').value;
        const maxVoltageDrop = parseFloat(document.getElementById('voltage-drop').value);

        if (!current || !length || !voltage || !maxVoltageDrop) {
            alert('Please fill in all required fields');
            return;
        }

        // Convert units to standard (Amperes, feet, volts)
        let currentAmps = current;
        if (currentUnit === 'mA') {
            currentAmps = current / 1000;
        }

        let lengthFeet = length;
        if (lengthUnit === 'm') {
            lengthFeet = length * 3.28084;
        } else if (lengthUnit === 'in') {
            lengthFeet = length / 12;
        }

        let voltageVolts = voltage;
        if (voltageUnit === 'kV') {
            voltageVolts = voltage * 1000;
        }

        // Calculate maximum allowable resistance for voltage drop
        const maxVoltageDrop_V = (maxVoltageDrop / 100) * voltageVolts;
        const maxResistance = maxVoltageDrop_V / currentAmps; // Ohms per 1000 feet
        const maxResistancePerFoot = maxResistance / (lengthFeet * 2 / 1000); // Round trip

        // Find appropriate wire gauge
        let recommendedGauge = null;
        let minGaugeForCurrent = null;

        // Check ampacity requirements
        for (const [gauge, data] of Object.entries(wireData)) {
            if (data.ampacity >= currentAmps * 1.25) { // 125% safety factor
                minGaugeForCurrent = gauge;
                break;
            }
        }

        // Check voltage drop requirements
        for (const [gauge, data] of Object.entries(wireData)) {
            const resistancePerFoot = data.resistance / 1000;
            if (resistancePerFoot <= maxResistancePerFoot) {
                recommendedGauge = gauge;
                break;
            }
        }

        // Use the larger gauge (smaller number) of the two requirements
        let finalGauge = recommendedGauge;
        if (minGaugeForCurrent && recommendedGauge) {
            const minGaugeNum = parseFloat(minGaugeForCurrent.replace(/0/g, '').length > 1 ?
                -minGaugeForCurrent.replace(/0/g, '').length : minGaugeForCurrent);
            const recGaugeNum = parseFloat(recommendedGauge.replace(/0/g, '').length > 1 ?
                -recommendedGauge.replace(/0/g, '').length : recommendedGauge);
            finalGauge = minGaugeNum <= recGaugeNum ? minGaugeForCurrent : recommendedGauge;
        } else if (minGaugeForCurrent) {
            finalGauge = minGaugeForCurrent;
        }

        if (!finalGauge) {
            finalGauge = '0000'; // Largest available
        }

        // Calculate actual values for the recommended gauge
        const selectedWire = wireData[finalGauge];
        const actualResistance = (selectedWire.resistance / 1000) * lengthFeet * 2; // Round trip
        const actualVoltageDrop = currentAmps * actualResistance;
        const actualVoltageDropPercent = (actualVoltageDrop / voltageVolts) * 100;

        // Display results
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = '';

        const results = [
            `Recommended Wire Gauge: ${finalGauge} AWG`,
            `Wire Diameter: ${selectedWire.diameter.toFixed(3)} mm`,
            `Cross-sectional Area: ${selectedWire.area.toFixed(2)} mm²`,
            `Current Capacity: ${selectedWire.ampacity} A`,
            `Resistance: ${actualResistance.toFixed(4)} Ω`,
            `Voltage Drop: ${actualVoltageDrop.toFixed(2)} V (${actualVoltageDropPercent.toFixed(2)}%)`,
            `Power Loss: ${(currentAmps * actualVoltageDrop).toFixed(2)} W`
        ];

        results.forEach(result => {
            const li = document.createElement('li');
            li.textContent = result;
            resultsList.appendChild(li);
        });

        document.getElementById('results').style.display = 'block';
    }

    function resetCalculator() {
        document.getElementById('current').value = '';
        document.getElementById('length').value = '';
        document.getElementById('voltage').value = '';
        document.getElementById('voltage-drop').value = '3';
        document.getElementById('current-unit').value = 'A';
        document.getElementById('length-unit').value = 'ft';
        document.getElementById('voltage-unit').value = 'V';
        document.getElementById('results').style.display = 'none';
    }
</script>

<!-- Content section -->
<div class="wire-calc">
    <div class="content-section">
        <h2>Understanding Wire Gauge and Electrical Safety</h2>
        <p>Wire gauge selection is critical for electrical safety and system performance. The American Wire Gauge (AWG) system standardizes wire sizes, where smaller numbers indicate larger wire diameters and higher current-carrying capacity.</p>

        <h3>Key Factors in Wire Selection</h3>
        <div class="highlight-box">
            <h3>Current Capacity (Ampacity)</h3>
            <p>The maximum current a wire can safely carry without overheating. This depends on wire size, insulation type, ambient temperature, and installation method.</p>
        </div>

        <div class="highlight-box">
            <h3>Voltage Drop</h3>
            <p>The reduction in voltage due to wire resistance. Excessive voltage drop can cause equipment malfunction and energy waste. Standard practice limits voltage drop to 3-5%.</p>
        </div>

        <h3>AWG Wire Specifications</h3>
        <table class="wire-table">
            <thead>
                <tr>
                    <th>AWG</th>
                    <th>Diameter (mm)</th>
                    <th>Area (mm²)</th>
                    <th>Resistance (Ω/1000ft)</th>
                    <th>Ampacity (A)</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>14</td><td>1.628</td><td>2.08</td><td>8.286</td><td>15</td></tr>
                <tr><td>12</td><td>2.053</td><td>3.31</td><td>5.211</td><td>20</td></tr>
                <tr><td>10</td><td>2.588</td><td>5.26</td><td>3.277</td><td>30</td></tr>
                <tr><td>8</td><td>3.264</td><td>8.37</td><td>2.061</td><td>40</td></tr>
                <tr><td>6</td><td>4.115</td><td>13.3</td><td>1.296</td><td>55</td></tr>
                <tr><td>4</td><td>5.189</td><td>21.2</td><td>0.815</td><td>70</td></tr>
                <tr><td>2</td><td>6.544</td><td>33.6</td><td>0.513</td><td>95</td></tr>
                <tr><td>1/0</td><td>8.251</td><td>53.5</td><td>0.323</td><td>125</td></tr>
                <tr><td>2/0</td><td>9.266</td><td>67.4</td><td>0.256</td><td>145</td></tr>
                <tr><td>4/0</td><td>11.68</td><td>107</td><td>0.161</td><td>195</td></tr>
            </tbody>
        </table>

        <h3>Calculation Methods</h3>
        <p><strong>Voltage Drop Formula:</strong> VD = 2 × K × I × L / CM</p>
        <ul>
            <li><strong>VD:</strong> Voltage drop in volts</li>
            <li><strong>K:</strong> Resistivity constant (12.9 for copper)</li>
            <li><strong>I:</strong> Current in amperes</li>
            <li><strong>L:</strong> One-way length in feet</li>
            <li><strong>CM:</strong> Circular mil area of conductor</li>
        </ul>

        <h3>Safety Considerations</h3>
        <ul>
            <li><strong>Overcurrent Protection:</strong> Always use appropriate fuses or circuit breakers</li>
            <li><strong>Temperature Derating:</strong> Reduce ampacity in high-temperature environments</li>
            <li><strong>Conduit Fill:</strong> Multiple wires in conduit require derating</li>
            <li><strong>Code Compliance:</strong> Follow NEC (National Electrical Code) requirements</li>
            <li><strong>Future Expansion:</strong> Consider oversizing for future load increases</li>
        </ul>

        <h3>Common Applications</h3>
        <ul>
            <li><strong>14 AWG:</strong> 15A circuits, lighting, outlets</li>
            <li><strong>12 AWG:</strong> 20A circuits, kitchen outlets, bathroom outlets</li>
            <li><strong>10 AWG:</strong> 30A circuits, electric water heaters, air conditioners</li>
            <li><strong>8 AWG:</strong> 40A circuits, electric ranges, large appliances</li>
            <li><strong>6 AWG:</strong> 55A circuits, electric furnaces, large motors</li>
        </ul>

        <h3>Installation Best Practices</h3>
        <ul>
            <li>Use copper wire for most residential applications</li>
            <li>Ensure proper wire termination and connections</li>
            <li>Maintain proper bend radius to prevent damage</li>
            <li>Use appropriate wire nuts and connectors</li>
            <li>Label circuits clearly for future maintenance</li>
        </ul>
    </div>
</div>
