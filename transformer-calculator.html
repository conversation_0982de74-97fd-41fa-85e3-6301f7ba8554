<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transformer Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free transformer calculator for turns ratio, voltage, current, and power calculations. Essential tool for electrical engineers and students.">
    <style>
        .transformer-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .transformer-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .transformer-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .transformer-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .transformer-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }

        .calculator {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        @media (max-width: 768px) {
            .transformer-calc {
                padding: 15px;
            }
            
            .transformer-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="transformer-calc">
        <header>
            <h1>Transformer Calculator</h1>
            <p class="intro">Calculate transformer parameters including turns ratio, voltage, current, and power relationships. Essential tool for electrical engineering design.</p>
        </header>

        <div class="calculator">
            <div class="calc-section">
                <h3>Primary Side Parameters</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="primaryVoltage">Primary Voltage (V)</label>
                        <input type="number" id="primaryVoltage" step="any" placeholder="Enter primary voltage">
                    </div>
                    <div class="input-field">
                        <label for="primaryCurrent">Primary Current (A)</label>
                        <input type="number" id="primaryCurrent" step="any" placeholder="Enter primary current">
                    </div>
                    <div class="input-field">
                        <label for="primaryTurns">Primary Turns</label>
                        <input type="number" id="primaryTurns" step="1" placeholder="Enter primary turns">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Secondary Side Parameters</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="secondaryVoltage">Secondary Voltage (V)</label>
                        <input type="number" id="secondaryVoltage" step="any" placeholder="Enter secondary voltage">
                    </div>
                    <div class="input-field">
                        <label for="secondaryCurrent">Secondary Current (A)</label>
                        <input type="number" id="secondaryCurrent" step="any" placeholder="Enter secondary current">
                    </div>
                    <div class="input-field">
                        <label for="secondaryTurns">Secondary Turns</label>
                        <input type="number" id="secondaryTurns" step="1" placeholder="Enter secondary turns">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Additional Parameters</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="efficiency">Efficiency (%)</label>
                        <input type="number" id="efficiency" step="any" value="95" placeholder="Enter efficiency">
                    </div>
                    <div class="input-field">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="number" id="frequency" step="any" value="50" placeholder="Enter frequency">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>About Transformers</h2>
            <p>Transformers are electrical devices that transfer electrical energy between two or more circuits through electromagnetic induction. They are used to increase or decrease AC voltages while maintaining power (minus losses).</p>
            
            <div class="highlight-box">
                <h3>Key Formulas</h3>
                <p><strong>Turns Ratio:</strong> a = Np/Ns = Vp/Vs = Is/Ip</p>
                <p><strong>Power Relationship:</strong> Pp = Ps (ideal transformer)</p>
                <p><strong>Voltage Transformation:</strong> Vs = Vp × (Ns/Np)</p>
                <p><strong>Current Transformation:</strong> Is = Ip × (Np/Ns)</p>
            </div>
        </div>
    </div>

    <script>
        function calculate() {
            const vp = parseFloat(document.getElementById('primaryVoltage').value);
            const ip = parseFloat(document.getElementById('primaryCurrent').value);
            const np = parseFloat(document.getElementById('primaryTurns').value);
            const vs = parseFloat(document.getElementById('secondaryVoltage').value);
            const is = parseFloat(document.getElementById('secondaryCurrent').value);
            const ns = parseFloat(document.getElementById('secondaryTurns').value);
            const efficiency = parseFloat(document.getElementById('efficiency').value) || 95;
            const frequency = parseFloat(document.getElementById('frequency').value) || 50;
            
            let results = [];
            let turnsRatio = null;
            
            // Calculate turns ratio from available data
            if (np && ns) {
                turnsRatio = np / ns;
                results.push(`<div class="result-item"><strong>Turns Ratio (Np:Ns):</strong> ${turnsRatio.toFixed(3)}:1</div>`);
            } else if (vp && vs) {
                turnsRatio = vp / vs;
                results.push(`<div class="result-item"><strong>Turns Ratio (from voltages):</strong> ${turnsRatio.toFixed(3)}:1</div>`);
            } else if (is && ip) {
                turnsRatio = is / ip;
                results.push(`<div class="result-item"><strong>Turns Ratio (from currents):</strong> ${turnsRatio.toFixed(3)}:1</div>`);
            }
            
            // Calculate missing voltages
            if (vp && turnsRatio && !vs) {
                const calculatedVs = vp / turnsRatio;
                results.push(`<div class="result-item"><strong>Secondary Voltage:</strong> ${calculatedVs.toFixed(2)} V</div>`);
            } else if (vs && turnsRatio && !vp) {
                const calculatedVp = vs * turnsRatio;
                results.push(`<div class="result-item"><strong>Primary Voltage:</strong> ${calculatedVp.toFixed(2)} V</div>`);
            }
            
            // Calculate missing currents
            if (ip && turnsRatio && !is) {
                const calculatedIs = ip * turnsRatio;
                results.push(`<div class="result-item"><strong>Secondary Current:</strong> ${calculatedIs.toFixed(3)} A</div>`);
            } else if (is && turnsRatio && !ip) {
                const calculatedIp = is / turnsRatio;
                results.push(`<div class="result-item"><strong>Primary Current:</strong> ${calculatedIp.toFixed(3)} A</div>`);
            }
            
            // Calculate power
            const primaryPower = vp && ip ? vp * ip : null;
            const secondaryPower = vs && is ? vs * is : null;
            
            if (primaryPower) {
                results.push(`<div class="result-item"><strong>Primary Power:</strong> ${primaryPower.toFixed(2)} W</div>`);
                if (efficiency) {
                    const outputPower = primaryPower * (efficiency / 100);
                    results.push(`<div class="result-item"><strong>Secondary Power (with ${efficiency}% efficiency):</strong> ${outputPower.toFixed(2)} W</div>`);
                }
            }
            
            if (secondaryPower) {
                results.push(`<div class="result-item"><strong>Secondary Power:</strong> ${secondaryPower.toFixed(2)} W</div>`);
            }
            
            // Calculate missing turns
            if (turnsRatio && np && !ns) {
                const calculatedNs = np / turnsRatio;
                results.push(`<div class="result-item"><strong>Secondary Turns:</strong> ${Math.round(calculatedNs)}</div>`);
            } else if (turnsRatio && ns && !np) {
                const calculatedNp = ns * turnsRatio;
                results.push(`<div class="result-item"><strong>Primary Turns:</strong> ${Math.round(calculatedNp)}</div>`);
            }
            
            if (results.length === 0) {
                alert('Please enter sufficient values for calculation');
                return;
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function reset() {
            const inputs = ['primaryVoltage', 'primaryCurrent', 'primaryTurns', 'secondaryVoltage', 'secondaryCurrent', 'secondaryTurns'];
            inputs.forEach(id => document.getElementById(id).value = '');
            document.getElementById('efficiency').value = '95';
            document.getElementById('frequency').value = '50';
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
