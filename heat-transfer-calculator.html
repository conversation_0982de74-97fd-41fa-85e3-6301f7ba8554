<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Transfer Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free heat transfer calculator for conduction, convection, and radiation. Calculate heat transfer rates, thermal resistance, and temperature distribution.">
    <style>
        .heat-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .heat-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .heat-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .heat-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .heat-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }
        
        .transfer-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .transfer-btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .transfer-btn.active {
            background: #007cba;
            color: #fff;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1.1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            height: 48px;
        }

        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
        }
        
        .heat-visual {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .heat-diagram {
            font-size: 2rem;
            color: #007cba;
            margin: 20px 0;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #3498db;
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-secondary {
            background: #e74c3c;
            color: #fff;
        }

        .btn-secondary:hover {
            background: #c0392b;
        }
        
        .results {
            background: #f7fafc;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e1e8ed;
            margin-top: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .results h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4rem;
            font-weight: 600;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .formula-box {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .material-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .material-table th, .material-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .material-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .heat-calc {
                padding: 15px;
            }
            
            .heat-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
            
            .transfer-types {
                gap: 5px;
            }
            
            .transfer-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="heat-calc">
        <header>
            <h1>Heat Transfer Calculator</h1>
            <p class="intro">Calculate heat transfer rates for conduction, convection, and radiation. Analyze thermal resistance, temperature distribution, and heat exchanger performance.</p>
        </header>

        <div class="transfer-types">
            <button class="transfer-btn active" onclick="selectTransferType('conduction')">Conduction</button>
            <button class="transfer-btn" onclick="selectTransferType('convection')">Convection</button>
            <button class="transfer-btn" onclick="selectTransferType('radiation')">Radiation</button>
            <button class="transfer-btn" onclick="selectTransferType('composite')">Composite Wall</button>
        </div>

        <div class="heat-visual">
            <h3 id="transfer-title">Heat Conduction</h3>
            <div class="heat-diagram" id="heat-diagram">🔥 ➡️ 🧱 ➡️ ❄️</div>
            <p id="transfer-description">Heat transfer through solid materials by molecular vibration</p>
        </div>

        <div class="calculator">
            <div id="conduction-inputs">
                <div class="calc-section">
                    <h3>Conduction Parameters</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="thermalConductivity">Thermal Conductivity (k) - W/m⋅K</label>
                            <input type="number" id="thermalConductivity" step="any" placeholder="Enter k value">
                        </div>
                        <div class="input-field">
                            <label for="area">Area (A) - m²</label>
                            <input type="number" id="area" step="any" placeholder="Enter area">
                        </div>
                        <div class="input-field">
                            <label for="thickness">Thickness (L) - m</label>
                            <input type="number" id="thickness" step="any" placeholder="Enter thickness">
                        </div>
                    </div>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="tempHot">Hot Side Temperature (°C)</label>
                            <input type="number" id="tempHot" step="any" placeholder="Enter hot temperature">
                        </div>
                        <div class="input-field">
                            <label for="tempCold">Cold Side Temperature (°C)</label>
                            <input type="number" id="tempCold" step="any" placeholder="Enter cold temperature">
                        </div>
                        <div class="input-field">
                            <label for="material">Material</label>
                            <select id="material" onchange="updateMaterial()">
                                <option value="custom">Custom</option>
                                <option value="copper">Copper</option>
                                <option value="aluminum">Aluminum</option>
                                <option value="steel">Steel</option>
                                <option value="concrete">Concrete</option>
                                <option value="glass">Glass</option>
                                <option value="wood">Wood</option>
                                <option value="insulation">Insulation</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="convection-inputs" style="display: none;">
                <div class="calc-section">
                    <h3>Convection Parameters</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="convectionCoeff">Heat Transfer Coefficient (h) - W/m²⋅K</label>
                            <input type="number" id="convectionCoeff" step="any" placeholder="Enter h value">
                        </div>
                        <div class="input-field">
                            <label for="convectionArea">Surface Area (A) - m²</label>
                            <input type="number" id="convectionArea" step="any" placeholder="Enter area">
                        </div>
                        <div class="input-field">
                            <label for="surfaceTemp">Surface Temperature (°C)</label>
                            <input type="number" id="surfaceTemp" step="any" placeholder="Enter surface temp">
                        </div>
                    </div>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="fluidTemp">Fluid Temperature (°C)</label>
                            <input type="number" id="fluidTemp" step="any" placeholder="Enter fluid temp">
                        </div>
                        <div class="input-field">
                            <label for="convectionType">Convection Type</label>
                            <select id="convectionType" onchange="updateConvection()">
                                <option value="custom">Custom</option>
                                <option value="natural-air">Natural - Air</option>
                                <option value="forced-air">Forced - Air</option>
                                <option value="natural-water">Natural - Water</option>
                                <option value="forced-water">Forced - Water</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="radiation-inputs" style="display: none;">
                <div class="calc-section">
                    <h3>Radiation Parameters</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="emissivity">Emissivity (ε)</label>
                            <input type="number" id="emissivity" step="any" value="0.9" min="0" max="1" placeholder="Enter emissivity">
                        </div>
                        <div class="input-field">
                            <label for="radiationArea">Surface Area (A) - m²</label>
                            <input type="number" id="radiationArea" step="any" placeholder="Enter area">
                        </div>
                        <div class="input-field">
                            <label for="hotTemp">Hot Surface Temperature (°C)</label>
                            <input type="number" id="hotTemp" step="any" placeholder="Enter hot temp">
                        </div>
                    </div>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="coldTemp">Cold Surface Temperature (°C)</label>
                            <input type="number" id="coldTemp" step="any" placeholder="Enter cold temp">
                        </div>
                        <div class="input-field">
                            <label for="surfaceType">Surface Type</label>
                            <select id="surfaceType" onchange="updateSurface()">
                                <option value="custom">Custom</option>
                                <option value="blackbody">Black Body</option>
                                <option value="oxidized-steel">Oxidized Steel</option>
                                <option value="polished-aluminum">Polished Aluminum</option>
                                <option value="painted-surface">Painted Surface</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Heat Transfer Fundamentals</h2>
            <p>Heat transfer occurs through three primary mechanisms, each governed by specific physical laws:</p>
            
            <div class="highlight-box">
                <h3>Conduction (Fourier's Law)</h3>
                <div class="formula-box">
                    q = -kA(dT/dx)<br>
                    For steady state: q = kA(ΔT)/L<br>
                    Thermal Resistance: R = L/(kA)
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Convection (Newton's Law of Cooling)</h3>
                <div class="formula-box">
                    q = hA(Ts - T∞)<br>
                    Where h = heat transfer coefficient<br>
                    Ts = surface temperature, T∞ = fluid temperature
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Radiation (Stefan-Boltzmann Law)</h3>
                <div class="formula-box">
                    q = εσA(T₁⁴ - T₂⁴)<br>
                    Where σ = 5.67×10⁻⁸ W/m²⋅K⁴<br>
                    ε = emissivity, T in Kelvin
                </div>
            </div>
            
            <h2>Material Properties Reference</h2>
            <table class="material-table">
                <thead>
                    <tr>
                        <th>Material</th>
                        <th>k (W/m⋅K)</th>
                        <th>Typical Applications</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>Copper</td><td>400</td><td>Heat exchangers, electronics</td></tr>
                    <tr><td>Aluminum</td><td>240</td><td>Heat sinks, cookware</td></tr>
                    <tr><td>Steel</td><td>50</td><td>Structural, piping</td></tr>
                    <tr><td>Concrete</td><td>1.7</td><td>Buildings, thermal mass</td></tr>
                    <tr><td>Glass</td><td>1.4</td><td>Windows, containers</td></tr>
                    <tr><td>Wood</td><td>0.12</td><td>Construction, furniture</td></tr>
                    <tr><td>Insulation</td><td>0.04</td><td>Thermal barriers</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let currentTransferType = 'conduction';
        
        const materialProperties = {
            copper: 400,
            aluminum: 240,
            steel: 50,
            concrete: 1.7,
            glass: 1.4,
            wood: 0.12,
            insulation: 0.04
        };
        
        const convectionCoefficients = {
            'natural-air': 5,
            'forced-air': 25,
            'natural-water': 500,
            'forced-water': 2000
        };
        
        const emissivityValues = {
            blackbody: 1.0,
            'oxidized-steel': 0.8,
            'polished-aluminum': 0.05,
            'painted-surface': 0.9
        };
        
        function selectTransferType(type) {
            currentTransferType = type;
            
            // Update active button
            document.querySelectorAll('.transfer-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide input sections
            document.getElementById('conduction-inputs').style.display = type === 'conduction' ? 'block' : 'none';
            document.getElementById('convection-inputs').style.display = type === 'convection' ? 'block' : 'none';
            document.getElementById('radiation-inputs').style.display = type === 'radiation' ? 'block' : 'none';
            
            // Update visual and descriptions
            const titles = {
                conduction: 'Heat Conduction',
                convection: 'Heat Convection',
                radiation: 'Heat Radiation',
                composite: 'Composite Wall'
            };
            
            const diagrams = {
                conduction: '🔥 ➡️ 🧱 ➡️ ❄️',
                convection: '🔥 ➡️ 🌊 ➡️ ❄️',
                radiation: '🔥 ↗️ 📡 ↘️ ❄️',
                composite: '🔥 ➡️ 🧱🧱🧱 ➡️ ❄️'
            };
            
            const descriptions = {
                conduction: 'Heat transfer through solid materials by molecular vibration',
                convection: 'Heat transfer by fluid motion (natural or forced)',
                radiation: 'Heat transfer by electromagnetic waves',
                composite: 'Heat transfer through multiple layers in series'
            };
            
            document.getElementById('transfer-title').textContent = titles[type];
            document.getElementById('heat-diagram').textContent = diagrams[type];
            document.getElementById('transfer-description').textContent = descriptions[type];
            
            if (type === 'composite') {
                alert('Composite wall calculator coming soon! Please use individual heat transfer modes.');
                // Reset to conduction
                document.querySelector('.transfer-btn').click();
            }
        }
        
        function updateMaterial() {
            const material = document.getElementById('material').value;
            if (material !== 'custom' && materialProperties[material]) {
                document.getElementById('thermalConductivity').value = materialProperties[material];
            }
        }
        
        function updateConvection() {
            const type = document.getElementById('convectionType').value;
            if (type !== 'custom' && convectionCoefficients[type]) {
                document.getElementById('convectionCoeff').value = convectionCoefficients[type];
            }
        }
        
        function updateSurface() {
            const surface = document.getElementById('surfaceType').value;
            if (surface !== 'custom' && emissivityValues[surface]) {
                document.getElementById('emissivity').value = emissivityValues[surface];
            }
        }
        
        function calculate() {
            let results = [];
            
            if (currentTransferType === 'conduction') {
                const k = parseFloat(document.getElementById('thermalConductivity').value);
                const A = parseFloat(document.getElementById('area').value);
                const L = parseFloat(document.getElementById('thickness').value);
                const T1 = parseFloat(document.getElementById('tempHot').value);
                const T2 = parseFloat(document.getElementById('tempCold').value);
                
                if (!k || !A || !L || isNaN(T1) || isNaN(T2)) {
                    alert('Please enter all conduction parameters');
                    return;
                }
                
                const deltaT = T1 - T2;
                const q = k * A * deltaT / L;
                const thermalResistance = L / (k * A);
                const heatFlux = q / A;
                
                results.push(`<div class="result-item"><strong>Heat Transfer Rate:</strong> ${formatValue(q)} W</div>`);
                results.push(`<div class="result-item"><strong>Heat Flux:</strong> ${formatValue(heatFlux)} W/m²</div>`);
                results.push(`<div class="result-item"><strong>Thermal Resistance:</strong> ${thermalResistance.toFixed(6)} K/W</div>`);
                results.push(`<div class="result-item"><strong>Temperature Difference:</strong> ${deltaT.toFixed(1)} °C</div>`);
                
            } else if (currentTransferType === 'convection') {
                const h = parseFloat(document.getElementById('convectionCoeff').value);
                const A = parseFloat(document.getElementById('convectionArea').value);
                const Ts = parseFloat(document.getElementById('surfaceTemp').value);
                const Tf = parseFloat(document.getElementById('fluidTemp').value);
                
                if (!h || !A || isNaN(Ts) || isNaN(Tf)) {
                    alert('Please enter all convection parameters');
                    return;
                }
                
                const deltaT = Ts - Tf;
                const q = h * A * deltaT;
                const thermalResistance = 1 / (h * A);
                const heatFlux = q / A;
                
                results.push(`<div class="result-item"><strong>Heat Transfer Rate:</strong> ${formatValue(q)} W</div>`);
                results.push(`<div class="result-item"><strong>Heat Flux:</strong> ${formatValue(heatFlux)} W/m²</div>`);
                results.push(`<div class="result-item"><strong>Convective Resistance:</strong> ${thermalResistance.toFixed(6)} K/W</div>`);
                results.push(`<div class="result-item"><strong>Temperature Difference:</strong> ${deltaT.toFixed(1)} °C</div>`);
                
            } else if (currentTransferType === 'radiation') {
                const epsilon = parseFloat(document.getElementById('emissivity').value);
                const A = parseFloat(document.getElementById('radiationArea').value);
                const T1 = parseFloat(document.getElementById('hotTemp').value) + 273.15; // Convert to Kelvin
                const T2 = parseFloat(document.getElementById('coldTemp').value) + 273.15; // Convert to Kelvin
                
                if (!epsilon || !A || !T1 || !T2) {
                    alert('Please enter all radiation parameters');
                    return;
                }
                
                const sigma = 5.67e-8; // Stefan-Boltzmann constant
                const q = epsilon * sigma * A * (Math.pow(T1, 4) - Math.pow(T2, 4));
                const heatFlux = q / A;
                const deltaT = T1 - T2;
                
                // Radiation heat transfer coefficient
                const hr = epsilon * sigma * (Math.pow(T1, 2) + Math.pow(T2, 2)) * (T1 + T2);
                
                results.push(`<div class="result-item"><strong>Heat Transfer Rate:</strong> ${formatValue(q)} W</div>`);
                results.push(`<div class="result-item"><strong>Heat Flux:</strong> ${formatValue(heatFlux)} W/m²</div>`);
                results.push(`<div class="result-item"><strong>Radiation Coefficient:</strong> ${hr.toFixed(3)} W/m²⋅K</div>`);
                results.push(`<div class="result-item"><strong>Temperature Difference:</strong> ${deltaT.toFixed(1)} K</div>`);
                results.push(`<div class="result-item"><strong>Hot Surface:</strong> ${(T1-273.15).toFixed(1)} °C (${T1.toFixed(1)} K)</div>`);
                results.push(`<div class="result-item"><strong>Cold Surface:</strong> ${(T2-273.15).toFixed(1)} °C (${T2.toFixed(1)} K)</div>`);
            }
            
            if (results.length === 0) {
                alert('Please enter valid values for calculation');
                return;
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (Math.abs(value) >= 1e6) return (value / 1e6).toFixed(2) + 'M';
            if (Math.abs(value) >= 1e3) return (value / 1e3).toFixed(2) + 'k';
            if (Math.abs(value) >= 1) return value.toFixed(3);
            if (Math.abs(value) >= 1e-3) return (value * 1e3).toFixed(2) + 'm';
            if (Math.abs(value) >= 1e-6) return (value * 1e6).toFixed(2) + 'μ';
            return (value * 1e9).toFixed(2) + 'n';
        }
        
        function reset() {
            // Reset conduction inputs
            document.getElementById('thermalConductivity').value = '';
            document.getElementById('area').value = '';
            document.getElementById('thickness').value = '';
            document.getElementById('tempHot').value = '';
            document.getElementById('tempCold').value = '';
            document.getElementById('material').selectedIndex = 0;
            
            // Reset convection inputs
            document.getElementById('convectionCoeff').value = '';
            document.getElementById('convectionArea').value = '';
            document.getElementById('surfaceTemp').value = '';
            document.getElementById('fluidTemp').value = '';
            document.getElementById('convectionType').selectedIndex = 0;
            
            // Reset radiation inputs
            document.getElementById('emissivity').value = '0.9';
            document.getElementById('radiationArea').value = '';
            document.getElementById('hotTemp').value = '';
            document.getElementById('coldTemp').value = '';
            document.getElementById('surfaceType').selectedIndex = 0;
            
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
