<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engineering Unit Converter - SKR Electronics Lab</title>
    <meta name="description" content="Free engineering unit converter for length, area, volume, mass, force, pressure, energy, power, and electrical units. Essential tool for engineers.">
    <style>
        .unit-converter * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .unit-converter {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .unit-converter header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .unit-converter h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .unit-converter .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }
        
        .converter-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .tab-btn {
            padding: 8px 15px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 15px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tab-btn.active {
            background: #007cba;
            color: #fff;
        }
        
        .converter-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .converter-row {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 15px;
            align-items: end;
            margin-bottom: 20px;
        }
        
        .unit-group {
            display: flex;
            flex-direction: column;
        }
        
        .unit-group label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .unit-input {
            display: flex;
            gap: 10px;
        }
        
        .unit-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .unit-input select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            min-width: 120px;
        }
        
        .unit-input input:focus, .unit-input select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .swap-btn {
            padding: 10px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .swap-btn:hover {
            background: #007cba;
            color: #fff;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .unit-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .category-box {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        
        .category-box h3 {
            color: #007cba;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        
        .category-box ul {
            list-style: none;
            font-size: 0.9rem;
            color: #666;
        }
        
        .category-box li {
            margin-bottom: 3px;
        }
        
        @media (max-width: 768px) {
            .unit-converter {
                padding: 15px;
            }
            
            .unit-converter h1 {
                font-size: 1.7rem;
            }
            
            .converter-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .swap-btn {
                justify-self: center;
            }
            
            .converter-tabs {
                gap: 3px;
            }
            
            .tab-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="unit-converter">
        <header>
            <h1>Engineering Unit Converter</h1>
            <p class="intro">Convert between different units of measurement for engineering calculations. Supports length, area, volume, mass, force, pressure, energy, power, and electrical units.</p>
        </header>

        <div class="converter-tabs">
            <button class="tab-btn active" onclick="switchCategory('length')">Length</button>
            <button class="tab-btn" onclick="switchCategory('area')">Area</button>
            <button class="tab-btn" onclick="switchCategory('volume')">Volume</button>
            <button class="tab-btn" onclick="switchCategory('mass')">Mass</button>
            <button class="tab-btn" onclick="switchCategory('force')">Force</button>
            <button class="tab-btn" onclick="switchCategory('pressure')">Pressure</button>
            <button class="tab-btn" onclick="switchCategory('energy')">Energy</button>
            <button class="tab-btn" onclick="switchCategory('power')">Power</button>
            <button class="tab-btn" onclick="switchCategory('electrical')">Electrical</button>
        </div>

        <div class="converter-section">
            <div class="converter-row">
                <div class="unit-group">
                    <label>From</label>
                    <div class="unit-input">
                        <input type="number" id="fromValue" step="any" placeholder="Enter value" oninput="convert()">
                        <select id="fromUnit" onchange="convert()"></select>
                    </div>
                </div>
                
                <button class="swap-btn" onclick="swapUnits()" title="Swap units">⇄</button>
                
                <div class="unit-group">
                    <label>To</label>
                    <div class="unit-input">
                        <input type="number" id="toValue" step="any" placeholder="Result" readonly>
                        <select id="toUnit" onchange="convert()"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2>Supported Unit Categories</h2>
            <p>This converter supports a wide range of engineering units across multiple categories:</p>
            
            <div class="unit-categories">
                <div class="category-box">
                    <h3>Length</h3>
                    <ul>
                        <li>Meters, Kilometers</li>
                        <li>Feet, Inches, Miles</li>
                        <li>Millimeters, Centimeters</li>
                        <li>Micrometers, Nanometers</li>
                    </ul>
                </div>
                
                <div class="category-box">
                    <h3>Area</h3>
                    <ul>
                        <li>Square meters, Hectares</li>
                        <li>Square feet, Acres</li>
                        <li>Square inches, Square miles</li>
                        <li>Square millimeters</li>
                    </ul>
                </div>
                
                <div class="category-box">
                    <h3>Volume</h3>
                    <ul>
                        <li>Liters, Cubic meters</li>
                        <li>Gallons, Cubic feet</li>
                        <li>Milliliters, Cubic inches</li>
                        <li>Fluid ounces, Pints</li>
                    </ul>
                </div>
                
                <div class="category-box">
                    <h3>Mass & Weight</h3>
                    <ul>
                        <li>Kilograms, Grams</li>
                        <li>Pounds, Ounces</li>
                        <li>Tons, Stones</li>
                        <li>Milligrams, Micrograms</li>
                    </ul>
                </div>
                
                <div class="category-box">
                    <h3>Force</h3>
                    <ul>
                        <li>Newtons, Kilonewtons</li>
                        <li>Pounds-force</li>
                        <li>Dynes, Kilograms-force</li>
                        <li>Tons-force</li>
                    </ul>
                </div>
                
                <div class="category-box">
                    <h3>Pressure</h3>
                    <ul>
                        <li>Pascals, Kilopascals</li>
                        <li>PSI, Atmospheres</li>
                        <li>Bar, Torr</li>
                        <li>mmHg, inHg</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const unitData = {
            length: {
                'm': 1,
                'km': 1000,
                'cm': 0.01,
                'mm': 0.001,
                'μm': 1e-6,
                'nm': 1e-9,
                'ft': 0.3048,
                'in': 0.0254,
                'yd': 0.9144,
                'mi': 1609.34
            },
            area: {
                'm²': 1,
                'km²': 1e6,
                'cm²': 1e-4,
                'mm²': 1e-6,
                'ft²': 0.092903,
                'in²': 6.4516e-4,
                'yd²': 0.836127,
                'acre': 4046.86,
                'hectare': 10000
            },
            volume: {
                'm³': 1,
                'L': 0.001,
                'mL': 1e-6,
                'ft³': 0.0283168,
                'in³': 1.63871e-5,
                'gal': 0.00378541,
                'qt': 0.000946353,
                'pt': 0.000473176,
                'fl oz': 2.95735e-5
            },
            mass: {
                'kg': 1,
                'g': 0.001,
                'mg': 1e-6,
                'μg': 1e-9,
                'lb': 0.453592,
                'oz': 0.0283495,
                'ton': 1000,
                'stone': 6.35029
            },
            force: {
                'N': 1,
                'kN': 1000,
                'lbf': 4.44822,
                'kgf': 9.80665,
                'dyne': 1e-5,
                'tonf': 9806.65
            },
            pressure: {
                'Pa': 1,
                'kPa': 1000,
                'MPa': 1e6,
                'bar': 1e5,
                'psi': 6894.76,
                'atm': 101325,
                'torr': 133.322,
                'mmHg': 133.322,
                'inHg': 3386.39
            },
            energy: {
                'J': 1,
                'kJ': 1000,
                'MJ': 1e6,
                'Wh': 3600,
                'kWh': 3.6e6,
                'cal': 4.184,
                'kcal': 4184,
                'BTU': 1055.06,
                'ft·lbf': 1.35582
            },
            power: {
                'W': 1,
                'kW': 1000,
                'MW': 1e6,
                'hp': 745.7,
                'BTU/h': 0.293071,
                'ft·lbf/s': 1.35582
            },
            electrical: {
                'V': 1,
                'kV': 1000,
                'mV': 0.001,
                'A': 1,
                'mA': 0.001,
                'μA': 1e-6,
                'Ω': 1,
                'kΩ': 1000,
                'MΩ': 1e6
            }
        };
        
        let currentCategory = 'length';
        
        function switchCategory(category) {
            currentCategory = category;
            
            // Update active tab
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Populate unit dropdowns
            const fromSelect = document.getElementById('fromUnit');
            const toSelect = document.getElementById('toUnit');
            
            fromSelect.innerHTML = '';
            toSelect.innerHTML = '';
            
            Object.keys(unitData[category]).forEach(unit => {
                fromSelect.innerHTML += `<option value="${unit}">${unit}</option>`;
                toSelect.innerHTML += `<option value="${unit}">${unit}</option>`;
            });
            
            // Set default selections
            if (toSelect.options.length > 1) {
                toSelect.selectedIndex = 1;
            }
            
            // Clear values
            document.getElementById('fromValue').value = '';
            document.getElementById('toValue').value = '';
        }
        
        function convert() {
            const fromValue = parseFloat(document.getElementById('fromValue').value);
            const fromUnit = document.getElementById('fromUnit').value;
            const toUnit = document.getElementById('toUnit').value;
            
            if (!fromValue || !fromUnit || !toUnit) {
                document.getElementById('toValue').value = '';
                return;
            }
            
            const fromFactor = unitData[currentCategory][fromUnit];
            const toFactor = unitData[currentCategory][toUnit];
            
            const result = (fromValue * fromFactor) / toFactor;
            document.getElementById('toValue').value = result.toExponential ? 
                (result < 1e-6 || result > 1e6 ? result.toExponential(6) : result.toPrecision(8)) : 
                result;
        }
        
        function swapUnits() {
            const fromUnit = document.getElementById('fromUnit');
            const toUnit = document.getElementById('toUnit');
            const fromValue = document.getElementById('fromValue');
            const toValue = document.getElementById('toValue');
            
            // Swap units
            const tempUnit = fromUnit.value;
            fromUnit.value = toUnit.value;
            toUnit.value = tempUnit;
            
            // Swap values
            const tempValue = fromValue.value;
            fromValue.value = toValue.value;
            toValue.value = tempValue;
            
            convert();
        }
        
        // Initialize
        switchCategory('length');
    </script>
</body>
</html>
