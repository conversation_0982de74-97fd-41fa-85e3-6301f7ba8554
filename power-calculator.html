<!-- Power Calculator for WordPress -->
<style>
    .power-calc * { margin: 0; padding: 0; box-sizing: border-box; -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1); }
    .power-calc { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif; line-height: 1.6; color: #333; background-color: #fff; padding: 25px; max-width: 900px; margin: 0 auto; border-radius: 12px; box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08); }
    .power-calc header { text-align: center; margin-bottom: 25px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
    .power-calc h1 { color: #2c3e50; margin-bottom: 12px; font-size: 2rem; line-height: 1.3; }
    .power-calc .intro { font-size: 1rem; color: #666; margin-bottom: 5px; }
    .power-calc .calculator { background-color: #f7fafc; padding: 30px; border-radius: 10px; margin-bottom: 25px; border: 1px solid #e1e8ed; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); }
    .power-calc .input-group { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 20px; }
    .power-calc .input-field { display: flex; flex-direction: column; }
    .power-calc .input-field label { margin-bottom: 10px; color: #2c3e50; font-weight: 500; font-size: 1.05rem; }
    .power-calc .input-with-unit { display: flex; gap: 10px; align-items: center; }
    .power-calc .input-with-unit input { flex: 3; padding: 12px 15px; border: 1px solid #ddd; border-radius: 8px; font-size: 1.1rem; transition: border-color 0.2s ease, box-shadow 0.2s ease; height: 48px; }
    .power-calc .input-with-unit input:focus { outline: none; border-color: #3498db; box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15); }
    .power-calc .input-with-unit select { flex: 1; padding: 12px 15px; border: 1px solid #ddd; border-radius: 8px; font-size: 1.1rem; background-color: #fff; min-width: 60px; max-width: 80px; transition: border-color 0.2s ease, box-shadow 0.2s ease; height: 48px; cursor: pointer; }
    .power-calc .input-with-unit select:focus { outline: none; border-color: #3498db; box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15); }
    .power-calc .button-group { display: flex; gap: 15px; margin-bottom: 20px; }
    .power-calc .btn { padding: 14px 25px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; flex: 1; font-weight: 500; transition: all 0.2s ease; height: 52px; }
    .power-calc .calculate { background-color: #3498db; color: white; }
    .power-calc .calculate:hover { background-color: #2980b9; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
    .power-calc .reset { background-color: #e74c3c; color: white; }
    .power-calc .reset:hover { background-color: #c0392b; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
    .power-calc .results { background-color: #fff; padding: 20px; border-radius: 8px; margin-top: 20px; border: 1px solid #e1e8ed; }
    .power-calc .results h3 { color: #2c3e50; margin-bottom: 15px; font-size: 1.2rem; }
    .power-calc .results ul { list-style: none; }
    .power-calc .results li { margin-bottom: 12px; color: #2c3e50; padding: 12px 16px; background-color: #f0f6fa; border-radius: 8px; font-weight: 500; font-size: 1.1rem; }
    .power-calc .formulas { background-color: #f7fafc; padding: 30px; border-radius: 10px; margin-bottom: 25px; border: 1px solid #e1e8ed; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); }
    .power-calc .formulas h2 { color: #2c3e50; margin-bottom: 20px; font-size: 1.5rem; text-align: center; }
    .power-calc .formula-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
    .power-calc .formula-item { text-align: center; padding: 20px; background-color: #fff; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.08); transition: transform 0.2s ease, box-shadow 0.2s ease; }
    .power-calc .formula-item:hover { transform: translateY(-3px); box-shadow: 0 5px 10px rgba(0,0,0,0.1); }
    .power-calc .formula-item h3 { color: #2c3e50; margin-bottom: 10px; font-size: 1.3rem; }
    .power-calc .formula-item p { font-size: 1rem; color: #555; }
    .power-calc .content-section { background-color: #f7fafc; padding: 30px; border-radius: 10px; margin-top: 25px; border: 1px solid #e1e8ed; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); }
    .power-calc .content-section h2 { color: #2c3e50; margin-bottom: 18px; font-size: 1.5rem; border-bottom: 1px solid #eee; padding-bottom: 8px; }
    .power-calc .content-section h3 { color: #2c3e50; margin: 20px 0 10px; font-size: 1.25rem; }
    .power-calc .content-section p { margin-bottom: 15px; color: #555; line-height: 1.7; }
    .power-calc .content-section strong { color: #2c3e50; font-weight: 600; }
    .power-calc .content-section ol, .power-calc .content-section ul { margin-bottom: 20px; padding-left: 25px; }
    .power-calc .content-section li { margin-bottom: 8px; color: #555; line-height: 1.7; }
    .power-calc .ad-spacer { height: 15px; width: 100%; margin: 25px 0; text-align: center; clear: both; }
    .power-calc .highlight-box { background-color: #edf7ff; border-left: 4px solid #3498db; padding: 15px 20px; margin: 20px 0; border-radius: 0 8px 8px 0; }
    .power-calc .highlight-box h3 { margin-top: 0; color: #2c3e50; }
    @media (max-width: 768px) { .power-calc { padding: 15px; } .power-calc h1 { font-size: 1.5rem; } .power-calc .calculator, .power-calc .formulas, .power-calc .content-section { padding: 15px; } .power-calc .input-group { grid-template-columns: 1fr; gap: 16px; } .power-calc .formula-grid { grid-template-columns: 1fr; gap: 12px; } }
</style>

<div class="power-calc">
    <header>
        <h1>Electrical Power Calculator</h1>
        <p class="intro">Calculate electrical power, voltage, current, and resistance using various power formulas. Enter any two known values to find the others.</p>
    </header>

    <div class="calculator">
        <div class="input-group">
            <div class="input-field">
                <label for="power">Power (P)</label>
                <div class="input-with-unit">
                    <input type="number" id="power" placeholder="Enter power">
                    <select id="power-unit">
                        <option value="W">W</option>
                        <option value="mW">mW</option>
                        <option value="kW">kW</option>
                        <option value="MW">MW</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="voltage">Voltage (V)</label>
                <div class="input-with-unit">
                    <input type="number" id="voltage" placeholder="Enter voltage">
                    <select id="voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                        <option value="kV">kV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="current">Current (I)</label>
                <div class="input-with-unit">
                    <input type="number" id="current" placeholder="Enter current">
                    <select id="current-unit">
                        <option value="A">A</option>
                        <option value="mA">mA</option>
                        <option value="µA">µA</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="resistance">Resistance (R)</label>
                <div class="input-with-unit">
                    <input type="number" id="resistance" placeholder="Enter resistance">
                    <select id="resistance-unit">
                        <option value="Ω">Ω</option>
                        <option value="kΩ">kΩ</option>
                        <option value="MΩ">MΩ</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="button-group">
            <button id="calculate" class="btn calculate">Calculate</button>
            <button id="reset" class="btn reset">Reset</button>
        </div>
        <div class="results" id="results">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <div class="formulas">
        <h2>Power Calculation Formulas</h2>
        <div class="formula-grid">
            <div class="formula-item">
                <h3>P = V × I</h3>
                <p>Power = Voltage × Current</p>
            </div>
            <div class="formula-item">
                <h3>P = I² × R</h3>
                <p>Power = Current² × Resistance</p>
            </div>
            <div class="formula-item">
                <h3>P = V² / R</h3>
                <p>Power = Voltage² / Resistance</p>
            </div>
            <div class="formula-item">
                <h3>P = √(P × R)</h3>
                <p>Voltage from Power & Resistance</p>
            </div>
        </div>
    </div>

    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Understanding Electrical Power</h2>
        <p>Electrical power is the rate at which electrical energy is consumed or produced in a circuit. It's measured in watts (W) and represents the amount of energy transferred per unit time. Understanding power calculations is essential for circuit design, component selection, and energy efficiency analysis.</p>

        <div class="highlight-box">
            <h3>Key Concept</h3>
            <p>Power in electrical circuits can be calculated using three fundamental relationships: P = V × I, P = I² × R, and P = V² / R. These formulas are derived from Ohm's Law and provide different ways to calculate power based on available measurements.</p>
        </div>

        <h2>Applications</h2>
        <ul>
            <li>Component power rating verification</li>
            <li>Heat dissipation calculations</li>
            <li>Energy consumption analysis</li>
            <li>Battery life estimation</li>
            <li>Power supply design</li>
            <li>Motor and load calculations</li>
        </ul>

        <h2>Safety Considerations</h2>
        <ul>
            <li>Always ensure components can handle calculated power levels</li>
            <li>Consider derating factors for temperature and reliability</li>
            <li>Use appropriate heat sinks for high-power components</li>
            <li>Verify power supply capacity before connecting loads</li>
        </ul>
    </div>
</div>

<script>
(function() {
    const powerInput = document.getElementById('power');
    const voltageInput = document.getElementById('voltage');
    const currentInput = document.getElementById('current');
    const resistanceInput = document.getElementById('resistance');
    const powerUnit = document.getElementById('power-unit');
    const voltageUnit = document.getElementById('voltage-unit');
    const currentUnit = document.getElementById('current-unit');
    const resistanceUnit = document.getElementById('resistance-unit');
    const calculateBtn = document.getElementById('calculate');
    const resetBtn = document.getElementById('reset');
    const resultsContent = document.getElementById('results-content');

    calculateBtn.addEventListener('click', calculate);
    resetBtn.addEventListener('click', reset);

    document.querySelectorAll('.power-calc input').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') { calculate(); e.preventDefault(); }
        });
    });

    const unitFactors = {
        power: { 'W': 1, 'mW': 0.001, 'kW': 1000, 'MW': 1000000 },
        voltage: { 'V': 1, 'mV': 0.001, 'kV': 1000 },
        current: { 'A': 1, 'mA': 0.001, 'µA': 0.000001 },
        resistance: { 'Ω': 1, 'kΩ': 1000, 'MΩ': 1000000 }
    };

    function convertToBase(value, unit, type) { return value * unitFactors[type][unit]; }
    function convertFromBase(value, unit, type) { return value / unitFactors[type][unit]; }
    function formatNumber(num) { return num < 0.01 && num > 0 ? num.toExponential(2) : num > 1000000 ? num.toExponential(2) : num.toFixed(2); }

    function countNonEmptyInputs() {
        let count = 0;
        [powerInput, voltageInput, currentInput, resistanceInput].forEach(input => {
            if (input.value !== '') count++;
        });
        return count;
    }

    function validateInputs() {
        const inputs = [powerInput, voltageInput, currentInput, resistanceInput];
        for (let input of inputs) {
            if (input.value !== '' && (isNaN(input.value) || parseFloat(input.value) <= 0)) return false;
        }
        return true;
    }

    function calculate() {
        resultsContent.innerHTML = '';

        if (countNonEmptyInputs() !== 2) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter exactly two values to calculate the others.</p>';
            return;
        }

        if (!validateInputs()) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter valid positive numbers.</p>';
            return;
        }

        const p = powerInput.value ? convertToBase(parseFloat(powerInput.value), powerUnit.value, 'power') : null;
        const v = voltageInput.value ? convertToBase(parseFloat(voltageInput.value), voltageUnit.value, 'voltage') : null;
        const i = currentInput.value ? convertToBase(parseFloat(currentInput.value), currentUnit.value, 'current') : null;
        const r = resistanceInput.value ? convertToBase(parseFloat(resistanceInput.value), resistanceUnit.value, 'resistance') : null;

        let results = [];

        try {
            if (p !== null && v !== null) {
                const calcI = p / v;
                const calcR = v * v / p;
                results.push(`Current (I) = ${formatNumber(convertFromBase(calcI, currentUnit.value, 'current'))} ${currentUnit.value}`);
                results.push(`Resistance (R) = ${formatNumber(convertFromBase(calcR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
            } else if (p !== null && i !== null) {
                const calcV = p / i;
                const calcR = p / (i * i);
                results.push(`Voltage (V) = ${formatNumber(convertFromBase(calcV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
                results.push(`Resistance (R) = ${formatNumber(convertFromBase(calcR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
            } else if (p !== null && r !== null) {
                const calcI = Math.sqrt(p / r);
                const calcV = Math.sqrt(p * r);
                results.push(`Current (I) = ${formatNumber(convertFromBase(calcI, currentUnit.value, 'current'))} ${currentUnit.value}`);
                results.push(`Voltage (V) = ${formatNumber(convertFromBase(calcV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
            } else if (v !== null && i !== null) {
                const calcP = v * i;
                const calcR = v / i;
                results.push(`Power (P) = ${formatNumber(convertFromBase(calcP, powerUnit.value, 'power'))} ${powerUnit.value}`);
                results.push(`Resistance (R) = ${formatNumber(convertFromBase(calcR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
            } else if (v !== null && r !== null) {
                const calcI = v / r;
                const calcP = v * v / r;
                results.push(`Current (I) = ${formatNumber(convertFromBase(calcI, currentUnit.value, 'current'))} ${currentUnit.value}`);
                results.push(`Power (P) = ${formatNumber(convertFromBase(calcP, powerUnit.value, 'power'))} ${powerUnit.value}`);
            } else if (i !== null && r !== null) {
                const calcV = i * r;
                const calcP = i * i * r;
                results.push(`Voltage (V) = ${formatNumber(convertFromBase(calcV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
                results.push(`Power (P) = ${formatNumber(convertFromBase(calcP, powerUnit.value, 'power'))} ${powerUnit.value}`);
            }
        } catch (error) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Error in calculation. Please check your inputs.</p>';
            return;
        }

        let html = '<ul>';
        results.forEach(result => { html += `<li>${result}</li>`; });
        html += '</ul>';
        resultsContent.innerHTML = html;
    }

    function reset() {
        powerInput.value = '';
        voltageInput.value = '';
        currentInput.value = '';
        resistanceInput.value = '';
        powerUnit.value = 'W';
        voltageUnit.value = 'V';
        currentUnit.value = 'A';
        resistanceUnit.value = 'Ω';
        resultsContent.innerHTML = '';
    }

    [powerInput, voltageInput, currentInput, resistanceInput].forEach(input => {
        input.addEventListener('keypress', (e) => {
            if (!/[\d.]/.test(e.key) && !e.ctrlKey && !e.metaKey) e.preventDefault();
        });
    });
})();
</script>