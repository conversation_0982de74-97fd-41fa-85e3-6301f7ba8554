<!-- Voltage Divider Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .voltage-divider-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .voltage-divider-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }

    /* Header styles */
    .voltage-divider-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .voltage-divider-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }

    .voltage-divider-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }

    /* Calculator section */
    .voltage-divider-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .voltage-divider-calc .input-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .voltage-divider-calc .input-field {
        display: flex;
        flex-direction: column;
    }

    .voltage-divider-calc .input-field label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }

    .voltage-divider-calc .input-with-unit {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .voltage-divider-calc .input-with-unit input {
        flex: 3;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
    }

    .voltage-divider-calc .input-with-unit input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .voltage-divider-calc .input-with-unit select {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        min-width: 60px;
        max-width: 80px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
        cursor: pointer;
    }

    .voltage-divider-calc .input-with-unit select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .voltage-divider-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .voltage-divider-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }

    .voltage-divider-calc .calculate {
        background-color: #3498db;
        color: white;
    }

    .voltage-divider-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .voltage-divider-calc .reset {
        background-color: #e74c3c;
        color: white;
    }

    .voltage-divider-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Results section */
    .voltage-divider-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }

    .voltage-divider-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .voltage-divider-calc .results ul {
        list-style: none;
    }

    .voltage-divider-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }

    /* Formula section */
    .voltage-divider-calc .formulas {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .voltage-divider-calc .formulas h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
    }

    .voltage-divider-calc .formula-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .voltage-divider-calc .formula-item {
        text-align: center;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .voltage-divider-calc .formula-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    .voltage-divider-calc .formula-item h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }

    .voltage-divider-calc .formula-item p {
        font-size: 1rem;
        color: #555;
    }

    /* Content section */
    .voltage-divider-calc .content-section {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-top: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .voltage-divider-calc .content-section h2 {
        color: #2c3e50;
        margin-bottom: 18px;
        font-size: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }

    .voltage-divider-calc .content-section h3 {
        color: #2c3e50;
        margin: 20px 0 10px;
        font-size: 1.25rem;
    }

    .voltage-divider-calc .content-section p {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.7;
    }

    .voltage-divider-calc .content-section strong {
        color: #2c3e50;
        font-weight: 600;
    }

    .voltage-divider-calc .content-section ol,
    .voltage-divider-calc .content-section ul {
        margin-bottom: 20px;
        padding-left: 25px;
    }

    .voltage-divider-calc .content-section li {
        margin-bottom: 8px;
        color: #555;
        line-height: 1.7;
    }

    /* Ad placement spacers */
    .voltage-divider-calc .ad-spacer {
        height: 15px;
        width: 100%;
        margin: 25px 0;
        text-align: center;
        clear: both;
    }

    /* Highlight box for important content */
    .voltage-divider-calc .highlight-box {
        background-color: #edf7ff;
        border-left: 4px solid #3498db;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }

    .voltage-divider-calc .highlight-box h3 {
        margin-top: 0;
        color: #2c3e50;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .voltage-divider-calc {
            padding: 15px;
            margin: 0 auto;
            border-radius: 8px;
        }

        .voltage-divider-calc h1 {
            font-size: 1.5rem;
        }

        .voltage-divider-calc .calculator,
        .voltage-divider-calc .formulas,
        .voltage-divider-calc .content-section {
            padding: 15px;
        }

        .voltage-divider-calc .input-group {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .voltage-divider-calc .formula-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }

    @media (max-width: 480px) {
        .voltage-divider-calc {
            padding: 12px;
        }

        .voltage-divider-calc .input-with-unit {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }

        .voltage-divider-calc .input-with-unit input {
            min-height: 44px;
            flex: 3;
        }

        .voltage-divider-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
            min-height: 44px;
        }

        .voltage-divider-calc .formula-item {
            padding: 12px;
        }

        .voltage-divider-calc .results li {
            padding: 10px;
        }
    }

    @media (max-width: 350px) {
        .voltage-divider-calc .input-with-unit {
            flex-direction: column;
            gap: 6px;
        }

        .voltage-divider-calc .input-with-unit select {
            width: 100%;
        }

        .voltage-divider-calc .button-group {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

<div class="voltage-divider-calc">
    <header>
        <h1>Voltage Divider Calculator</h1>
        <p class="intro">Calculate output voltage, resistor values, and current in voltage divider circuits. Enter any three known values to find the fourth.</p>
    </header>

    <div class="calculator">
        <div class="input-group">
            <div class="input-field">
                <label for="input-voltage">Input Voltage (Vin)</label>
                <div class="input-with-unit">
                    <input type="number" id="input-voltage" placeholder="Enter input voltage">
                    <select id="input-voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                        <option value="kV">kV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="output-voltage">Output Voltage (Vout)</label>
                <div class="input-with-unit">
                    <input type="number" id="output-voltage" placeholder="Enter output voltage">
                    <select id="output-voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                        <option value="kV">kV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="r1">Resistor R1 (Upper)</label>
                <div class="input-with-unit">
                    <input type="number" id="r1" placeholder="Enter R1 value">
                    <select id="r1-unit">
                        <option value="Ω">Ω</option>
                        <option value="kΩ">kΩ</option>
                        <option value="MΩ">MΩ</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="r2">Resistor R2 (Lower)</label>
                <div class="input-with-unit">
                    <input type="number" id="r2" placeholder="Enter R2 value">
                    <select id="r2-unit">
                        <option value="Ω">Ω</option>
                        <option value="kΩ">kΩ</option>
                        <option value="MΩ">MΩ</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="button-group">
            <button id="calculate" class="btn calculate">Calculate</button>
            <button id="reset" class="btn reset">Reset</button>
        </div>
        <div class="results" id="results">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <div class="formulas">
        <h2>Voltage Divider Formulas</h2>
        <div class="formula-grid">
            <div class="formula-item">
                <h3>Vout = Vin × (R2 / (R1 + R2))</h3>
                <p>Output Voltage Formula</p>
            </div>
            <div class="formula-item">
                <h3>R1 = R2 × ((Vin - Vout) / Vout)</h3>
                <p>Upper Resistor Formula</p>
            </div>
            <div class="formula-item">
                <h3>R2 = R1 × (Vout / (Vin - Vout))</h3>
                <p>Lower Resistor Formula</p>
            </div>
            <div class="formula-item">
                <h3>I = Vin / (R1 + R2)</h3>
                <p>Current Through Circuit</p>
            </div>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Understanding Voltage Dividers</h2>
        <p>A voltage divider is a simple circuit that produces an output voltage that is a fraction of its input voltage. It consists of two resistors connected in series, with the output voltage taken from the connection point between them.</p>

        <div class="highlight-box">
            <h3>Key Principle</h3>
            <p>The output voltage is proportional to the ratio of the lower resistor (R2) to the total resistance (R1 + R2). This makes voltage dividers useful for creating reference voltages and scaling down voltages for measurement.</p>
        </div>

        <h2>How to Use the Calculator</h2>
        <ol>
            <li>Enter any three known values from input voltage, output voltage, R1, or R2</li>
            <li>Select the appropriate units for your input values</li>
            <li>Click "Calculate" to find the missing value and circuit current</li>
            <li>Use "Reset" to clear all inputs and start over</li>
        </ol>

        <h2>Common Applications</h2>
        <ul>
            <li>Creating reference voltages for analog circuits</li>
            <li>Voltage scaling for ADC inputs</li>
            <li>Biasing transistors and operational amplifiers</li>
            <li>Level shifting in digital circuits</li>
            <li>Sensor signal conditioning</li>
        </ul>

        <h2>Design Considerations</h2>
        <ul>
            <li><strong>Loading Effect:</strong> The output impedance equals R1||R2, which can affect accuracy when loaded</li>
            <li><strong>Power Consumption:</strong> Current flows continuously through both resistors</li>
            <li><strong>Temperature Stability:</strong> Use resistors with matching temperature coefficients</li>
            <li><strong>Tolerance:</strong> Resistor tolerances affect output voltage accuracy</li>
        </ul>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Practical Design Examples</h2>

        <h3>Example 1: Creating a 3.3V Reference from 5V</h3>
        <p>To create a 3.3V output from a 5V input, we need Vout/Vin = 3.3/5 = 0.66. If we choose R2 = 10kΩ, then R1 = R2 × ((Vin - Vout) / Vout) = 10kΩ × ((5 - 3.3) / 3.3) = 5.15kΩ. Use a standard 5.1kΩ resistor.</p>

        <h3>Example 2: ADC Input Scaling</h3>
        <p>To scale a 0-10V signal to 0-3.3V for a microcontroller ADC, use the ratio 3.3/10 = 0.33. With R2 = 3.3kΩ, calculate R1 = 3.3kΩ × ((10 - 3.3) / 3.3) = 6.7kΩ. Use standard 6.8kΩ resistor.</p>

        <h3>Example 3: Battery Voltage Monitoring</h3>
        <p>To monitor a 12V battery with a 3.3V ADC, create a 4:1 divider. Use R1 = 30kΩ and R2 = 10kΩ for a ratio of 10/(30+10) = 0.25, giving 3V output at 12V input with some safety margin.</p>

        <div class="highlight-box">
            <h3>Design Tip</h3>
            <p>For battery monitoring and low-power applications, use high-value resistors (>10kΩ) to minimize current consumption. For high-frequency applications, use lower values to reduce the RC time constant with parasitic capacitance.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Troubleshooting Common Issues</h2>

        <h3>Output Voltage Too Low</h3>
        <ul>
            <li>Check for excessive loading on the output</li>
            <li>Verify resistor values and connections</li>
            <li>Ensure input voltage is stable and correct</li>
            <li>Consider using a buffer amplifier for low-impedance output</li>
        </ul>

        <h3>Inaccurate Voltage Division</h3>
        <ul>
            <li>Use precision resistors (1% or better tolerance)</li>
            <li>Account for temperature effects in critical applications</li>
            <li>Minimize parasitic resistances in connections</li>
            <li>Consider the input impedance of measuring instruments</li>
        </ul>

        <h3>Excessive Power Consumption</h3>
        <ul>
            <li>Increase resistor values to reduce current</li>
            <li>Consider using a switched divider for intermittent measurements</li>
            <li>Use a voltage reference IC for precision applications</li>
            <li>Implement power management in battery-operated devices</li>
        </ul>

        <div class="highlight-box">
            <h3>Safety Note</h3>
            <p>When working with high voltages, ensure proper insulation and safety precautions. Use appropriate voltage ratings for all components and follow electrical safety guidelines.</p>
        </div>
    </div>
</div>

<script>
(function() {
    // Get DOM elements
    const inputVoltageInput = document.getElementById('input-voltage');
    const outputVoltageInput = document.getElementById('output-voltage');
    const r1Input = document.getElementById('r1');
    const r2Input = document.getElementById('r2');
    const inputVoltageUnit = document.getElementById('input-voltage-unit');
    const outputVoltageUnit = document.getElementById('output-voltage-unit');
    const r1Unit = document.getElementById('r1-unit');
    const r2Unit = document.getElementById('r2-unit');
    const calculateBtn = document.getElementById('calculate');
    const resetBtn = document.getElementById('reset');
    const resultsContent = document.getElementById('results-content');

    // Add event listeners
    calculateBtn.addEventListener('click', calculate);
    resetBtn.addEventListener('click', reset);

    // Add keyboard support - enter key triggers calculation
    document.querySelectorAll('.voltage-divider-calc input').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculate();
                e.preventDefault();
            }
        });
    });

    // Unit conversion factors
    const unitFactors = {
        voltage: {
            'V': 1,
            'mV': 0.001,
            'kV': 1000
        },
        resistance: {
            'Ω': 1,
            'kΩ': 1000,
            'MΩ': 1000000
        }
    };

    // Function to convert value to base unit
    function convertToBase(value, unit, type) {
        return value * unitFactors[type][unit];
    }

    // Function to convert from base unit to target unit
    function convertFromBase(value, unit, type) {
        return value / unitFactors[type][unit];
    }

    // Function to count non-empty inputs
    function countNonEmptyInputs() {
        let count = 0;
        const inputs = [inputVoltageInput, outputVoltageInput, r1Input, r2Input];
        inputs.forEach(input => {
            if (input.value !== '') count++;
        });
        return count;
    }

    // Function to validate inputs
    function validateInputs() {
        const inputs = [inputVoltageInput, outputVoltageInput, r1Input, r2Input];
        for (let input of inputs) {
            if (input.value !== '' && (isNaN(input.value) || parseFloat(input.value) <= 0)) {
                return false;
            }
        }
        return true;
    }

    // Format number with appropriate decimal places
    function formatNumber(num) {
        // Handle very small numbers
        if (num < 0.01 && num > 0) {
            return num.toExponential(2);
        }
        // Handle very large numbers
        if (num > 1000000) {
            return num.toExponential(2);
        }
        // Regular numbers
        return num.toFixed(2);
    }

    // Function to calculate missing values
    function calculate() {
        // Clear previous results
        resultsContent.innerHTML = '';

        // Check if exactly three values are provided
        if (countNonEmptyInputs() !== 3) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter exactly three values to calculate the fourth.</p>';
            return;
        }

        // Validate inputs
        if (!validateInputs()) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter valid positive numbers.</p>';
            return;
        }

        // Get input values and convert to base units
        const vin = inputVoltageInput.value ? convertToBase(parseFloat(inputVoltageInput.value), inputVoltageUnit.value, 'voltage') : null;
        const vout = outputVoltageInput.value ? convertToBase(parseFloat(outputVoltageInput.value), outputVoltageUnit.value, 'voltage') : null;
        const r1 = r1Input.value ? convertToBase(parseFloat(r1Input.value), r1Unit.value, 'resistance') : null;
        const r2 = r2Input.value ? convertToBase(parseFloat(r2Input.value), r2Unit.value, 'resistance') : null;

        // Calculate missing value in base units
        let results = [];
        let calculatedValue = null;
        let current = null;

        try {
            // Case 1: Missing input voltage (Vin)
            if (vin === null && vout !== null && r1 !== null && r2 !== null) {
                calculatedValue = vout * (r1 + r2) / r2;
                current = calculatedValue / (r1 + r2);
                results.push(`Input Voltage (Vin) = ${formatNumber(convertFromBase(calculatedValue, inputVoltageUnit.value, 'voltage'))} ${inputVoltageUnit.value}`);
            }
            // Case 2: Missing output voltage (Vout)
            else if (vout === null && vin !== null && r1 !== null && r2 !== null) {
                calculatedValue = vin * r2 / (r1 + r2);
                current = vin / (r1 + r2);
                results.push(`Output Voltage (Vout) = ${formatNumber(convertFromBase(calculatedValue, outputVoltageUnit.value, 'voltage'))} ${outputVoltageUnit.value}`);
            }
            // Case 3: Missing R1
            else if (r1 === null && vin !== null && vout !== null && r2 !== null) {
                if (vout >= vin) {
                    resultsContent.innerHTML = '<p style="color: #e74c3c;">Output voltage cannot be greater than or equal to input voltage.</p>';
                    return;
                }
                calculatedValue = r2 * (vin - vout) / vout;
                current = vin / (calculatedValue + r2);
                results.push(`Resistor R1 = ${formatNumber(convertFromBase(calculatedValue, r1Unit.value, 'resistance'))} ${r1Unit.value}`);
            }
            // Case 4: Missing R2
            else if (r2 === null && vin !== null && vout !== null && r1 !== null) {
                if (vout >= vin) {
                    resultsContent.innerHTML = '<p style="color: #e74c3c;">Output voltage cannot be greater than or equal to input voltage.</p>';
                    return;
                }
                calculatedValue = r1 * vout / (vin - vout);
                current = vin / (r1 + calculatedValue);
                results.push(`Resistor R2 = ${formatNumber(convertFromBase(calculatedValue, r2Unit.value, 'resistance'))} ${r2Unit.value}`);
            }
            else {
                resultsContent.innerHTML = '<p style="color: #e74c3c;">Invalid combination of inputs. Please enter exactly three values.</p>';
                return;
            }

            // Add current calculation
            if (current !== null) {
                let currentUnit = 'A';
                let currentValue = current;

                if (current < 0.001) {
                    currentUnit = 'µA';
                    currentValue = current * 1000000;
                } else if (current < 1) {
                    currentUnit = 'mA';
                    currentValue = current * 1000;
                }

                results.push(`Circuit Current = ${formatNumber(currentValue)} ${currentUnit}`);
            }

            // Calculate power dissipation
            if (vin !== null && current !== null) {
                const totalPower = vin * current;
                let powerUnit = 'W';
                let powerValue = totalPower;

                if (totalPower < 0.001) {
                    powerUnit = 'µW';
                    powerValue = totalPower * 1000000;
                } else if (totalPower < 1) {
                    powerUnit = 'mW';
                    powerValue = totalPower * 1000;
                }

                results.push(`Total Power = ${formatNumber(powerValue)} ${powerUnit}`);
            }

        } catch (error) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Error in calculation. Please check your inputs.</p>';
            return;
        }

        // Display results
        displayResults(results);
    }

    // Function to display results
    function displayResults(results) {
        if (results.length === 0) {
            resultsContent.innerHTML = '<p>Please enter three values to calculate the fourth.</p>';
            return;
        }

        let html = '<ul>';
        results.forEach(result => {
            html += `<li>${result}</li>`;
        });
        html += '</ul>';
        resultsContent.innerHTML = html;
    }

    // Function to reset calculator
    function reset() {
        inputVoltageInput.value = '';
        outputVoltageInput.value = '';
        r1Input.value = '';
        r2Input.value = '';
        inputVoltageUnit.value = 'V';
        outputVoltageUnit.value = 'V';
        r1Unit.value = 'Ω';
        r2Unit.value = 'Ω';
        resultsContent.innerHTML = '';
    }

    // Add input validation on keypress
    [inputVoltageInput, outputVoltageInput, r1Input, r2Input].forEach(input => {
        input.addEventListener('keypress', (e) => {
            // Allow only numbers, decimal point, and control characters
            if (!/[\d.]/.test(e.key) && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
            }
        });
    });
})();
</script>