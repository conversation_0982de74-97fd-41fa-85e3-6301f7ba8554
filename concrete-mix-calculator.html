<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concrete Mix Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free concrete mix calculator for determining cement, sand, aggregate, and water ratios. Calculate material quantities for different concrete grades and volumes.">
    <style>
        .concrete-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .concrete-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .concrete-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .concrete-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .concrete-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .grade-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .grade-btn {
            padding: 10px 15px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .grade-btn.active {
            background: #007cba;
            color: #fff;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .mix-info {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
        }
        
        .mix-info h4 {
            color: #007cba;
            margin-bottom: 10px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .material-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .material-table th, .material-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .material-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        @media (max-width: 768px) {
            .concrete-calc {
                padding: 15px;
            }
            
            .concrete-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
            
            .grade-selector {
                gap: 5px;
            }
            
            .grade-btn {
                padding: 8px 12px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="concrete-calc">
        <header>
            <h1>Concrete Mix Calculator</h1>
            <p class="intro">Calculate concrete mix proportions and material quantities for different grades of concrete. Determine cement, sand, aggregate, and water requirements for your project.</p>
        </header>

        <div class="grade-selector">
            <button class="grade-btn active" onclick="selectGrade('M10')">M10</button>
            <button class="grade-btn" onclick="selectGrade('M15')">M15</button>
            <button class="grade-btn" onclick="selectGrade('M20')">M20</button>
            <button class="grade-btn" onclick="selectGrade('M25')">M25</button>
            <button class="grade-btn" onclick="selectGrade('M30')">M30</button>
            <button class="grade-btn" onclick="selectGrade('M35')">M35</button>
            <button class="grade-btn" onclick="selectGrade('M40')">M40</button>
            <button class="grade-btn" onclick="selectGrade('custom')">Custom</button>
        </div>

        <div class="mix-info" id="mix-info">
            <h4>M10 Grade Concrete</h4>
            <p><strong>Compressive Strength:</strong> 10 N/mm² (1450 PSI)</p>
            <p><strong>Mix Ratio:</strong> 1:3:6 (Cement:Sand:Aggregate)</p>
            <p><strong>Applications:</strong> Plain concrete work, leveling course, non-structural work</p>
        </div>

        <div class="calculator">
            <div class="calc-section">
                <h3>Project Requirements</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="volume">Concrete Volume (m³)</label>
                        <input type="number" id="volume" step="any" placeholder="Enter volume">
                    </div>
                    <div class="input-field">
                        <label for="wastage">Wastage Factor (%)</label>
                        <input type="number" id="wastage" step="any" value="5" placeholder="Enter wastage %">
                    </div>
                    <div class="input-field">
                        <label for="units">Output Units</label>
                        <select id="units">
                            <option value="metric">Metric (kg, m³)</option>
                            <option value="imperial">Imperial (lbs, ft³)</option>
                            <option value="bags">Bags & Cubic Yards</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div id="custom-mix" style="display: none;">
                <div class="calc-section">
                    <h3>Custom Mix Ratio</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="cementRatio">Cement Ratio</label>
                            <input type="number" id="cementRatio" step="any" value="1" placeholder="Enter cement ratio">
                        </div>
                        <div class="input-field">
                            <label for="sandRatio">Sand Ratio</label>
                            <input type="number" id="sandRatio" step="any" value="2" placeholder="Enter sand ratio">
                        </div>
                        <div class="input-field">
                            <label for="aggregateRatio">Aggregate Ratio</label>
                            <input type="number" id="aggregateRatio" step="any" value="4" placeholder="Enter aggregate ratio">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="calc-section">
                <h3>Material Properties</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="waterCementRatio">Water-Cement Ratio</label>
                        <input type="number" id="waterCementRatio" step="any" value="0.5" placeholder="Enter W/C ratio">
                    </div>
                    <div class="input-field">
                        <label for="cementDensity">Cement Density (kg/m³)</label>
                        <input type="number" id="cementDensity" step="any" value="1440" placeholder="Enter density">
                    </div>
                    <div class="input-field">
                        <label for="sandDensity">Sand Density (kg/m³)</label>
                        <input type="number" id="sandDensity" step="any" value="1600" placeholder="Enter density">
                    </div>
                </div>
                <div class="input-group">
                    <div class="input-field">
                        <label for="aggregateDensity">Aggregate Density (kg/m³)</label>
                        <input type="number" id="aggregateDensity" step="any" value="1600" placeholder="Enter density">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate Mix</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Material Requirements</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Concrete Grade Reference</h2>
            <table class="material-table">
                <thead>
                    <tr>
                        <th>Grade</th>
                        <th>Strength (N/mm²)</th>
                        <th>Mix Ratio</th>
                        <th>Applications</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>M10</td><td>10</td><td>1:3:6</td><td>Plain concrete, leveling</td></tr>
                    <tr><td>M15</td><td>15</td><td>1:2:4</td><td>Mass concrete, foundations</td></tr>
                    <tr><td>M20</td><td>20</td><td>1:1.5:3</td><td>Reinforced concrete, slabs</td></tr>
                    <tr><td>M25</td><td>25</td><td>1:1:2</td><td>Structural concrete, beams</td></tr>
                    <tr><td>M30</td><td>30</td><td>Design Mix</td><td>High-rise buildings</td></tr>
                    <tr><td>M35</td><td>35</td><td>Design Mix</td><td>Prestressed concrete</td></tr>
                    <tr><td>M40</td><td>40</td><td>Design Mix</td><td>Heavy-duty structures</td></tr>
                </tbody>
            </table>
            
            <div class="highlight-box">
                <h3>Important Notes</h3>
                <p><strong>Water-Cement Ratio:</strong> Lower ratios increase strength but reduce workability</p>
                <p><strong>Aggregate Size:</strong> Maximum size should not exceed 1/4 of minimum structural dimension</p>
                <p><strong>Curing:</strong> Proper curing is essential for achieving design strength</p>
                <p><strong>Quality Control:</strong> Use clean materials and proper mixing procedures</p>
            </div>
        </div>
    </div>

    <script>
        let currentGrade = 'M10';
        
        const concreteGrades = {
            M10: { strength: 10, ratio: [1, 3, 6], wcRatio: 0.6, applications: 'Plain concrete work, leveling course, non-structural work' },
            M15: { strength: 15, ratio: [1, 2, 4], wcRatio: 0.55, applications: 'Mass concrete work, foundations, footings' },
            M20: { strength: 20, ratio: [1, 1.5, 3], wcRatio: 0.5, applications: 'Reinforced concrete, slabs, beams, columns' },
            M25: { strength: 25, ratio: [1, 1, 2], wcRatio: 0.45, applications: 'Structural concrete, beams, columns' },
            M30: { strength: 30, ratio: [1, 0.75, 1.5], wcRatio: 0.4, applications: 'High-rise buildings, bridges' },
            M35: { strength: 35, ratio: [1, 0.6, 1.2], wcRatio: 0.38, applications: 'Prestressed concrete, heavy structures' },
            M40: { strength: 40, ratio: [1, 0.5, 1], wcRatio: 0.35, applications: 'Heavy-duty structures, marine works' }
        };
        
        function selectGrade(grade) {
            currentGrade = grade;
            
            // Update active button
            document.querySelectorAll('.grade-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide custom mix inputs
            document.getElementById('custom-mix').style.display = grade === 'custom' ? 'block' : 'none';
            
            if (grade !== 'custom') {
                const gradeData = concreteGrades[grade];
                
                // Update mix info
                document.getElementById('mix-info').innerHTML = `
                    <h4>${grade} Grade Concrete</h4>
                    <p><strong>Compressive Strength:</strong> ${gradeData.strength} N/mm² (${(gradeData.strength * 145).toFixed(0)} PSI)</p>
                    <p><strong>Mix Ratio:</strong> ${gradeData.ratio.join(':')} (Cement:Sand:Aggregate)</p>
                    <p><strong>Applications:</strong> ${gradeData.applications}</p>
                `;
                
                // Update water-cement ratio
                document.getElementById('waterCementRatio').value = gradeData.wcRatio;
            } else {
                document.getElementById('mix-info').innerHTML = `
                    <h4>Custom Mix Design</h4>
                    <p>Enter your custom mix proportions and material properties below.</p>
                `;
            }
        }
        
        function calculate() {
            const volume = parseFloat(document.getElementById('volume').value);
            const wastage = parseFloat(document.getElementById('wastage').value) || 0;
            const units = document.getElementById('units').value;
            const wcRatio = parseFloat(document.getElementById('waterCementRatio').value);
            const cementDensity = parseFloat(document.getElementById('cementDensity').value);
            const sandDensity = parseFloat(document.getElementById('sandDensity').value);
            const aggregateDensity = parseFloat(document.getElementById('aggregateDensity').value);
            
            if (!volume || !wcRatio || !cementDensity || !sandDensity || !aggregateDensity) {
                alert('Please enter all required values');
                return;
            }
            
            // Get mix ratios
            let cementRatio, sandRatio, aggregateRatio;
            
            if (currentGrade === 'custom') {
                cementRatio = parseFloat(document.getElementById('cementRatio').value) || 1;
                sandRatio = parseFloat(document.getElementById('sandRatio').value) || 2;
                aggregateRatio = parseFloat(document.getElementById('aggregateRatio').value) || 4;
            } else {
                const ratios = concreteGrades[currentGrade].ratio;
                cementRatio = ratios[0];
                sandRatio = ratios[1];
                aggregateRatio = ratios[2];
            }
            
            // Calculate total volume with wastage
            const totalVolume = volume * (1 + wastage / 100);
            
            // Calculate dry volume (typically 1.54 times wet volume for concrete)
            const dryVolume = totalVolume * 1.54;
            
            // Calculate individual volumes
            const totalRatio = cementRatio + sandRatio + aggregateRatio;
            const cementVolume = (cementRatio / totalRatio) * dryVolume;
            const sandVolume = (sandRatio / totalRatio) * dryVolume;
            const aggregateVolume = (aggregateRatio / totalRatio) * dryVolume;
            
            // Calculate weights
            const cementWeight = cementVolume * cementDensity;
            const sandWeight = sandVolume * sandDensity;
            const aggregateWeight = aggregateVolume * aggregateDensity;
            const waterWeight = cementWeight * wcRatio;
            
            let results = [];
            
            if (units === 'metric') {
                results.push(`<div class="result-item"><strong>Total Volume (with wastage):</strong> ${totalVolume.toFixed(2)} m³</div>`);
                results.push(`<div class="result-item"><strong>Cement:</strong> ${cementWeight.toFixed(0)} kg (${cementVolume.toFixed(2)} m³)</div>`);
                results.push(`<div class="result-item"><strong>Sand:</strong> ${sandWeight.toFixed(0)} kg (${sandVolume.toFixed(2)} m³)</div>`);
                results.push(`<div class="result-item"><strong>Aggregate:</strong> ${aggregateWeight.toFixed(0)} kg (${aggregateVolume.toFixed(2)} m³)</div>`);
                results.push(`<div class="result-item"><strong>Water:</strong> ${waterWeight.toFixed(0)} kg (${waterWeight.toFixed(0)} liters)</div>`);
                
            } else if (units === 'imperial') {
                const cementLbs = cementWeight * 2.20462;
                const sandLbs = sandWeight * 2.20462;
                const aggregateLbs = aggregateWeight * 2.20462;
                const waterLbs = waterWeight * 2.20462;
                const totalVolumeFt3 = totalVolume * 35.3147;
                
                results.push(`<div class="result-item"><strong>Total Volume (with wastage):</strong> ${totalVolumeFt3.toFixed(1)} ft³</div>`);
                results.push(`<div class="result-item"><strong>Cement:</strong> ${cementLbs.toFixed(0)} lbs</div>`);
                results.push(`<div class="result-item"><strong>Sand:</strong> ${sandLbs.toFixed(0)} lbs</div>`);
                results.push(`<div class="result-item"><strong>Aggregate:</strong> ${aggregateLbs.toFixed(0)} lbs</div>`);
                results.push(`<div class="result-item"><strong>Water:</strong> ${waterLbs.toFixed(0)} lbs</div>`);
                
            } else if (units === 'bags') {
                const cementBags = cementWeight / 50; // 50kg bags
                const totalVolumeYd3 = totalVolume * 1.30795;
                
                results.push(`<div class="result-item"><strong>Total Volume (with wastage):</strong> ${totalVolumeYd3.toFixed(2)} yd³</div>`);
                results.push(`<div class="result-item"><strong>Cement:</strong> ${cementBags.toFixed(1)} bags (50kg each)</div>`);
                results.push(`<div class="result-item"><strong>Sand:</strong> ${sandWeight.toFixed(0)} kg</div>`);
                results.push(`<div class="result-item"><strong>Aggregate:</strong> ${aggregateWeight.toFixed(0)} kg</div>`);
                results.push(`<div class="result-item"><strong>Water:</strong> ${waterWeight.toFixed(0)} liters</div>`);
            }
            
            // Add mix ratio and other info
            results.push(`<div class="result-item"><strong>Mix Ratio:</strong> ${cementRatio}:${sandRatio}:${aggregateRatio}</div>`);
            results.push(`<div class="result-item"><strong>Water-Cement Ratio:</strong> ${wcRatio}</div>`);
            results.push(`<div class="result-item"><strong>Total Weight:</strong> ${(cementWeight + sandWeight + aggregateWeight + waterWeight).toFixed(0)} kg</div>`);
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function reset() {
            document.getElementById('volume').value = '';
            document.getElementById('wastage').value = '5';
            document.getElementById('units').selectedIndex = 0;
            document.getElementById('waterCementRatio').value = '0.5';
            document.getElementById('cementDensity').value = '1440';
            document.getElementById('sandDensity').value = '1600';
            document.getElementById('aggregateDensity').value = '1600';
            document.getElementById('cementRatio').value = '1';
            document.getElementById('sandRatio').value = '2';
            document.getElementById('aggregateRatio').value = '4';
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
