<!-- 555 IC Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .ic555-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
    
    .ic555-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }
    
    /* Header styles */
    .ic555-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }
    
    .ic555-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .ic555-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }
    
    /* Mode selector */
    .ic555-calc .mode-selector {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;
        justify-content: center;
    }
    
    .ic555-calc .mode-btn {
        padding: 12px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
        background-color: #f0f0f0;
        color: #666;
    }
    
    .ic555-calc .mode-btn.active {
        background-color: #3498db;
        color: white;
    }
    
    /* Calculator section */
    .ic555-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ic555-calc .input-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .ic555-calc .input-field {
        display: flex;
        flex-direction: column;
    }
    
    .ic555-calc .input-field label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }
    
    .ic555-calc .input-with-unit {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .ic555-calc .input-with-unit input {
        flex: 3;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
    }
    
    .ic555-calc .input-with-unit input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .ic555-calc .input-with-unit select {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        min-width: 60px;
        max-width: 80px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
        cursor: pointer;
    }
    
    .ic555-calc .input-with-unit select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .ic555-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .ic555-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }
    
    .ic555-calc .calculate {
        background-color: #3498db;
        color: white;
    }
    
    .ic555-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .ic555-calc .reset {
        background-color: #e74c3c;
        color: white;
    }
    
    .ic555-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Results section */
    .ic555-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }
    
    .ic555-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }
    
    .ic555-calc .results ul {
        list-style: none;
    }
    
    .ic555-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }
    
    /* Formula section */
    .ic555-calc .formulas {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ic555-calc .formulas h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
    }
    
    .ic555-calc .formula-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .ic555-calc .formula-item {
        text-align: center;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .ic555-calc .formula-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
    
    .ic555-calc .formula-item h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .ic555-calc .formula-item p {
        font-size: 1rem;
        color: #555;
    }
    
    /* Content section */
    .ic555-calc .content-section {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-top: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ic555-calc .content-section h2 {
        color: #2c3e50;
        margin-bottom: 18px;
        font-size: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }
    
    .ic555-calc .content-section h3 {
        color: #2c3e50;
        margin: 20px 0 10px;
        font-size: 1.25rem;
    }
    
    .ic555-calc .content-section p {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.7;
    }
    
    .ic555-calc .content-section strong {
        color: #2c3e50;
        font-weight: 600;
    }
    
    .ic555-calc .content-section ol,
    .ic555-calc .content-section ul {
        margin-bottom: 20px;
        padding-left: 25px;
    }
    
    .ic555-calc .content-section li {
        margin-bottom: 8px;
        color: #555;
        line-height: 1.7;
    }
    
    /* Highlight box for important content */
    .ic555-calc .highlight-box {
        background-color: #edf7ff;
        border-left: 4px solid #3498db;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }
    
    .ic555-calc .highlight-box h3 {
        margin-top: 0;
        color: #2c3e50;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .ic555-calc {
            padding: 15px;
            margin: 0 auto;
            border-radius: 8px;
        }
    
        .ic555-calc h1 {
            font-size: 1.5rem;
        }
        
        .ic555-calc .calculator,
        .ic555-calc .formulas,
        .ic555-calc .content-section {
            padding: 15px;
        }
    
        .ic555-calc .input-group {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .ic555-calc .formula-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    
        .ic555-calc .input-field label {
            font-size: 1rem;
            margin-bottom: 8px;
        }
        
        .ic555-calc .input-with-unit input {
            flex: 2;
        }
        
        .ic555-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
        }
        
        .ic555-calc .btn {
            font-size: 1rem;
            padding: 12px;
            height: 44px;
        }
        
        .ic555-calc .results li {
            font-size: 1rem;
            padding: 10px;
        }
        
        .ic555-calc .formula-item h3 {
            font-size: 1.1rem;
        }
        
        .ic555-calc .formula-item p {
            font-size: 0.9rem;
        }
        
        .ic555-calc .content-section {
            padding: 20px;
        }
        
        .ic555-calc .content-section h2 {
            font-size: 1.3rem;
        }
        
        .ic555-calc .content-section h3 {
            font-size: 1.15rem;
        }
    }
    
    @media (max-width: 480px) {
        .ic555-calc {
            padding: 12px;
        }
        
        .ic555-calc .input-with-unit {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
        
        .ic555-calc .input-with-unit input {
            min-height: 44px;
            flex: 3;
        }
        
        .ic555-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
            min-height: 44px;
        }
        
        .ic555-calc .formula-item {
            padding: 12px;
        }
        
        .ic555-calc .results li {
            padding: 10px;
        }
    }
    
    @media (max-width: 350px) {
        .ic555-calc .input-with-unit {
            flex-direction: column;
            gap: 6px;
        }
        
        .ic555-calc .input-with-unit select {
            width: 100%;
        }
        
        .ic555-calc .button-group {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

<div class="ic555-calc">
    <header>
        <h1>555 IC Calculator</h1>
        <p class="intro">Calculate timing parameters for 555 IC in astable and bistable modes. Enter component values to determine frequency, duty cycle, and timing characteristics.</p>
    </header>

    <div class="mode-selector">
        <button class="mode-btn active" data-mode="astable">Astable Mode</button>
        <button class="mode-btn" data-mode="bistable">Bistable Mode</button>
    </div>

    <div class="calculator">
        <!-- Astable Mode Inputs -->
        <div id="astable-inputs">
            <div class="input-group">
                <div class="input-field">
                    <label for="r1">R1 (Resistor 1)</label>
                    <div class="input-with-unit">
                        <input type="number" id="r1" placeholder="Enter R1 value">
                        <select id="r1-unit">
                            <option value="Ω">Ω</option>
                            <option value="kΩ">kΩ</option>
                            <option value="MΩ">MΩ</option>
                        </select>
                    </div>
                </div>
                <div class="input-field">
                    <label for="r2">R2 (Resistor 2)</label>
                    <div class="input-with-unit">
                        <input type="number" id="r2" placeholder="Enter R2 value">
                        <select id="r2-unit">
                            <option value="Ω">Ω</option>
                            <option value="kΩ">kΩ</option>
                            <option value="MΩ">MΩ</option>
                        </select>
                    </div>
                </div>
                <div class="input-field">
                    <label for="c">C (Capacitor)</label>
                    <div class="input-with-unit">
                        <input type="number" id="c" placeholder="Enter C value">
                        <select id="c-unit">
                            <option value="F">F</option>
                            <option value="µF">µF</option>
                            <option value="nF">nF</option>
                            <option value="pF">pF</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bistable Mode Inputs -->
        <div id="bistable-inputs" style="display: none;">
            <div class="input-group">
                <div class="input-field">
                    <label for="r">R (Pull-up Resistor)</label>
                    <div class="input-with-unit">
                        <input type="number" id="r" placeholder="Enter R value">
                        <select id="r-unit">
                            <option value="Ω">Ω</option>
                            <option value="kΩ">kΩ</option>
                            <option value="MΩ">MΩ</option>
                        </select>
                    </div>
                </div>
                <div class="input-field">
                    <label for="c-bistable">C (Decoupling Capacitor)</label>
                    <div class="input-with-unit">
                        <input type="number" id="c-bistable" placeholder="Enter C value">
                        <select id="c-bistable-unit">
                            <option value="F">F</option>
                            <option value="µF">µF</option>
                            <option value="nF">nF</option>
                            <option value="pF">pF</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button id="calculate" class="btn calculate">Calculate</button>
            <button id="reset" class="btn reset">Reset</button>
        </div>
        <div class="results" id="results">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <div class="formulas">
        <h2>555 IC Formulas</h2>
        <div class="formula-grid">
            <div class="formula-item">
                <h3>Astable Mode</h3>
                <p>Frequency = 1.44 / ((R1 + 2R2) × C)</p>
                <p>Duty Cycle = (R1 + R2) / (R1 + 2R2)</p>
            </div>
            <div class="formula-item">
                <h3>Bistable Mode</h3>
                <p>Output State: Set/Reset controlled</p>
                <p>No timing calculations needed</p>
            </div>
        </div>
    </div>

    <div class="content-section">
        <h2>Understanding 555 IC Modes</h2>
        <p>The 555 IC is a versatile integrated circuit that can operate in two main modes:</p>
        
        <h3>Astable Mode</h3>
        <p>In astable mode, the 555 IC operates as a free-running oscillator, generating a continuous square wave output. The frequency and duty cycle are determined by the values of two resistors (R1 and R2) and a capacitor (C).</p>
        
        <div class="highlight-box">
            <h3>Key Features of Astable Mode</h3>
            <ul>
                <li>Continuous square wave output</li>
                <li>Adjustable frequency and duty cycle</li>
                <li>Requires two resistors and one capacitor</li>
                <li>Commonly used in timing applications</li>
            </ul>
        </div>
        
        <h3>Bistable Mode</h3>
        <p>In bistable mode, the 555 IC operates as a flip-flop, with two stable states controlled by Set and Reset inputs. This mode is useful for creating memory circuits and simple switches.</p>
        
        <div class="highlight-box">
            <h3>Key Features of Bistable Mode</h3>
            <ul>
                <li>Two stable output states</li>
                <li>Set and Reset controlled</li>
                <li>No timing components required</li>
                <li>Ideal for memory applications</li>
            </ul>
        </div>
    </div>

    <div class="content-section">
        <h2>How to Use the Calculator</h2>
        <ol>
            <li>Select the desired mode (Astable or Bistable)</li>
            <li>Enter the component values in the appropriate fields</li>
            <li>Select the correct units for each component</li>
            <li>Click "Calculate" to see the results</li>
            <li>Use "Reset" to clear all inputs</li>
        </ol>

        <div class="highlight-box">
            <h3>Important Notes</h3>
            <ul>
                <li>For astable mode, all three components (R1, R2, and C) are required</li>
                <li>For bistable mode, only the pull-up resistor and decoupling capacitor are needed</li>
                <li>Ensure component values are within the 555 IC's operating range</li>
                <li>Consider power supply voltage when selecting components</li>
            </ul>
        </div>
    </div>

    <div class="content-section">
        <h2>Common Applications</h2>
        <h3>Astable Mode Applications</h3>
        <ul>
            <li>LED flashers and blinkers</li>
            <li>Pulse generators</li>
            <li>Clock circuits</li>
            <li>Audio tone generators</li>
            <li>PWM controllers</li>
        </ul>
        
        <h3>Bistable Mode Applications</h3>
        <ul>
            <li>Touch switches</li>
            <li>Latches</li>
            <li>Memory circuits</li>
            <li>Toggle switches</li>
            <li>Debounce circuits</li>
        </ul>
    </div>
</div>

<script>
(function() {
    // Get DOM elements
    const modeBtns = document.querySelectorAll('.mode-btn');
    const astableInputs = document.getElementById('astable-inputs');
    const bistableInputs = document.getElementById('bistable-inputs');
    const calculateBtn = document.getElementById('calculate');
    const resetBtn = document.getElementById('reset');
    const resultsContent = document.getElementById('results-content');

    // Mode switching
    modeBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            modeBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            if (btn.dataset.mode === 'astable') {
                astableInputs.style.display = 'block';
                bistableInputs.style.display = 'none';
            } else {
                astableInputs.style.display = 'none';
                bistableInputs.style.display = 'block';
            }
        });
    });

    // Add event listeners
    calculateBtn.addEventListener('click', calculate);
    resetBtn.addEventListener('click', reset);

    // Unit conversion factors
    const unitFactors = {
        resistance: {
            'Ω': 1,
            'kΩ': 1000,
            'MΩ': 1000000
        },
        capacitance: {
            'F': 1,
            'µF': 0.000001,
            'nF': 0.000000001,
            'pF': 0.000000000001
        }
    };

    // Function to convert value to base unit
    function convertToBase(value, unit, type) {
        return value * unitFactors[type][unit];
    }

    // Function to validate inputs
    function validateInputs() {
        const activeMode = document.querySelector('.mode-btn.active').dataset.mode;
        
        if (activeMode === 'astable') {
            const r1 = document.getElementById('r1').value;
            const r2 = document.getElementById('r2').value;
            const c = document.getElementById('c').value;
            
            if (!r1 || !r2 || !c || isNaN(r1) || isNaN(r2) || isNaN(c)) {
                return false;
            }
        } else {
            const r = document.getElementById('r').value;
            const c = document.getElementById('c-bistable').value;
            
            if (!r || !c || isNaN(r) || isNaN(c)) {
                return false;
            }
        }
        return true;
    }

    // Function to calculate values
    function calculate() {
        // Clear previous results
        resultsContent.innerHTML = '';
        
        // Validate inputs
        if (!validateInputs()) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter valid values for all required components.</p>';
            return;
        }

        const activeMode = document.querySelector('.mode-btn.active').dataset.mode;
        
        if (activeMode === 'astable') {
            calculateAstable();
        } else {
            calculateBistable();
        }
    }

    // Function to calculate astable mode values
    function calculateAstable() {
        const r1 = convertToBase(
            parseFloat(document.getElementById('r1').value),
            document.getElementById('r1-unit').value,
            'resistance'
        );
        
        const r2 = convertToBase(
            parseFloat(document.getElementById('r2').value),
            document.getElementById('r2-unit').value,
            'resistance'
        );
        
        const c = convertToBase(
            parseFloat(document.getElementById('c').value),
            document.getElementById('c-unit').value,
            'capacitance'
        );

        // Calculate frequency
        const frequency = 1.44 / ((r1 + 2 * r2) * c);
        
        // Calculate duty cycle
        const dutyCycle = ((r1 + r2) / (r1 + 2 * r2)) * 100;
        
        // Calculate time high and low
        const timeHigh = 0.693 * (r1 + r2) * c;
        const timeLow = 0.693 * r2 * c;
        
        // Calculate period
        const period = timeHigh + timeLow;

        // Display results
        let html = '<ul>';
        html += `<li>Frequency: ${formatNumber(frequency)} Hz</li>`;
        html += `<li>Duty Cycle: ${formatNumber(dutyCycle)}%</li>`;
        html += `<li>Time High: ${formatNumber(timeHigh * 1000)} ms</li>`;
        html += `<li>Time Low: ${formatNumber(timeLow * 1000)} ms</li>`;
        html += `<li>Period: ${formatNumber(period * 1000)} ms</li>`;
        html += '</ul>';
        
        resultsContent.innerHTML = html;
    }

    // Function to calculate bistable mode values
    function calculateBistable() {
        const r = convertToBase(
            parseFloat(document.getElementById('r').value),
            document.getElementById('r-unit').value,
            'resistance'
        );
        
        const c = convertToBase(
            parseFloat(document.getElementById('c-bistable').value),
            document.getElementById('c-bistable-unit').value,
            'capacitance'
        );

        // Calculate minimum pulse width for reliable triggering
        const minPulseWidth = 0.1 * r * c;

        // Display results
        let html = '<ul>';
        html += `<li>Minimum Trigger Pulse Width: ${formatNumber(minPulseWidth * 1000)} ms</li>`;
        html += `<li>Recommended Pull-up Resistor: ${formatNumber(r)} Ω</li>`;
        html += `<li>Decoupling Capacitor: ${formatNumber(c * 1000000)} µF</li>`;
        html += '</ul>';
        
        resultsContent.innerHTML = html;
    }

    // Format number with appropriate decimal places
    function formatNumber(num) {
        if (num < 0.01) {
            return num.toExponential(2);
        }
        if (num > 1000000) {
            return num.toExponential(2);
        }
        return num.toFixed(2);
    }

    // Function to reset calculator
    function reset() {
        // Reset astable inputs
        document.getElementById('r1').value = '';
        document.getElementById('r2').value = '';
        document.getElementById('c').value = '';
        document.getElementById('r1-unit').value = 'Ω';
        document.getElementById('r2-unit').value = 'Ω';
        document.getElementById('c-unit').value = 'µF';
        
        // Reset bistable inputs
        document.getElementById('r').value = '';
        document.getElementById('c-bistable').value = '';
        document.getElementById('r-unit').value = 'Ω';
        document.getElementById('c-bistable-unit').value = 'µF';
        
        resultsContent.innerHTML = '';
    }

    // Add input validation
    document.querySelectorAll('.ic555-calc input').forEach(input => {
        input.addEventListener('keypress', (e) => {
            if (!/[\d.]/.test(e.key) && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
            }
        });
    });
})();
</script> 