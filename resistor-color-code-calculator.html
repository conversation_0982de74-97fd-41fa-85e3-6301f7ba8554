<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resistor Color Code Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free resistor color code calculator. Decode 4, 5, and 6 band resistors instantly. Learn resistor color codes with visual examples and educational content.">
    <style>
        /* Reset and base styles */
        .resistor-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .resistor-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }

        /* Header styles */
        .resistor-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .resistor-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }

        .resistor-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* Band selector */
        .resistor-calc .band-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .resistor-calc .band-btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .resistor-calc .band-btn.active {
            background: #007cba;
            color: #fff;
        }

        /* Calculator section */
        .resistor-calc .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        /* Resistor visual */
        .resistor-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 8px;
        }

        .resistor-body {
            width: 280px;
            height: 50px;
            background: #d4af37;
            border-radius: 25px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 15px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }

        .resistor-lead {
            width: 30px;
            height: 3px;
            background: #c0c0c0;
            border-radius: 2px;
        }

        .color-band {
            width: 12px;
            height: 40px;
            border-radius: 2px;
            border: 1px solid rgba(0,0,0,0.2);
            transition: all 0.2s;
            cursor: pointer;
        }

        .color-band:hover {
            transform: scale(1.05);
        }
        
        /* Color selectors */
        .color-selectors {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .color-group {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .color-group label {
            font-weight: 500;
            color: #333;
            font-size: 1rem;
            margin-bottom: 10px;
            display: block;
        }

        .color-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .color-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .color-option.selected {
            border-color: #007cba;
            transform: scale(1.05);
            box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.3);
        }
        
        /* Color definitions */
        .color-black { background-color: #000000; }
        .color-brown { background-color: #8B4513; }
        .color-red { background-color: #FF0000; }
        .color-orange { background-color: #FFA500; }
        .color-yellow { background-color: #FFFF00; }
        .color-green { background-color: #008000; }
        .color-blue { background-color: #0000FF; }
        .color-violet { background-color: #8A2BE2; }
        .color-grey { background-color: #808080; }
        .color-white { background-color: #FFFFFF; border: 1px solid #ccc; }
        .color-gold { background-color: #FFD700; }
        .color-silver { background-color: #C0C0C0; }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .resistor-calc .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .resistor-calc .calculate {
            background: #007cba;
            color: #fff;
        }

        .resistor-calc .btn:hover {
            background: #007cba;
            color: #fff;
        }

        .resistor-calc .calculate:hover {
            background: #005a8b;
        }

        .resistor-calc .reset {
            border-color: #dc3545;
            color: #dc3545;
        }

        .resistor-calc .reset:hover {
            background: #dc3545;
            color: #fff;
        }
        
        /* Results section */
        .resistor-calc .results {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e1e8ed;
        }
        
        .resistor-calc .results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .resistor-calc .results ul {
            list-style: none;
        }
        
        .resistor-calc .results li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }

        .resistor-calc .results li:last-child {
            border-bottom: none;
        }
        
        /* Reference section */
        .reference-section {
            background-color: #f7fafc;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .reference-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }
        
        .color-table {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        
        .color-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        }
        
        .color-table th,
        .color-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .color-table th {
            background-color: #3498db;
            color: white;
            font-weight: 600;
        }
        
        .color-table td:first-child {
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }
        
        .table-color-sample {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1px solid #ccc;
        }
        
        /* Content sections */
        .resistor-calc .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }

        .resistor-calc .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .resistor-calc .content-section h3 {
            color: #333;
            margin: 15px 0 8px;
            font-size: 1.1rem;
        }

        .resistor-calc .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }

        .resistor-calc .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }

        .resistor-calc .highlight-box h3 {
            margin-top: 0;
            color: #333;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .resistor-calc {
                padding: 15px;
            }
            
            .resistor-calc h1 {
                font-size: 1.5rem;
            }
            
            .resistor-body {
                width: 250px;
                height: 50px;
                padding: 0 15px;
            }
            
            .color-band {
                width: 12px;
                height: 40px;
            }
            
            .color-selectors {
                gap: 15px;
            }
            
            .color-options {
                gap: 6px;
            }
            
            .color-option {
                width: 25px;
                height: 25px;
            }
        }
        
        @media (max-width: 480px) {
            .resistor-calc {
                padding: 12px;
            }
            
            .band-selector {
                flex-direction: column;
                gap: 8px;
            }
            
            .resistor-body {
                width: 200px;
                height: 40px;
            }
            
            .color-band {
                width: 10px;
                height: 32px;
            }
            
            .button-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="resistor-calc">
        <header>
            <h1>Resistor Color Code Calculator</h1>
            <p class="intro">Decode resistor values using color bands. Select the number of bands and choose colors to calculate resistance, tolerance, and temperature coefficient.</p>
        </header>

        <div class="band-selector">
            <button class="band-btn" data-bands="4">4 Bands</button>
            <button class="band-btn active" data-bands="5">5 Bands</button>
            <button class="band-btn" data-bands="6">6 Bands</button>
        </div>

        <div class="calculator">
            <div class="resistor-visual">
                <div class="resistor-lead"></div>
                <div class="resistor-body">
                    <div class="color-band" id="band1" data-color="black"></div>
                    <div class="color-band" id="band2" data-color="black"></div>
                    <div class="color-band" id="band3" data-color="black"></div>
                    <div class="color-band" id="band4" data-color="black"></div>
                    <div class="color-band" id="band5" data-color="gold" style="display: none;"></div>
                    <div class="color-band" id="band6" data-color="brown" style="display: none;"></div>
                </div>
                <div class="resistor-lead"></div>
            </div>

            <div class="color-selectors" id="colorSelectors">
                <!-- Color selectors will be dynamically generated -->
            </div>

            <div class="button-group">
                <button id="calculate" class="btn calculate">Calculate</button>
                <button id="reset" class="btn reset">Reset</button>
            </div>

            <div class="results" id="results">
                <h3>Results</h3>
                <div id="results-content">
                    <p>Select colors to calculate resistance value</p>
                </div>
            </div>
        </div>

        <div class="reference-section">
            <h2>Color Code Reference</h2>
            <div class="color-table">
                <table>
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Digit</th>
                            <th>Multiplier</th>
                            <th>Tolerance</th>
                            <th>Temp. Coeff.</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="table-color-sample color-black"></span> Black</td>
                            <td>0</td>
                            <td>×1</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-brown"></span> Brown</td>
                            <td>1</td>
                            <td>×10</td>
                            <td>±1%</td>
                            <td>100 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-red"></span> Red</td>
                            <td>2</td>
                            <td>×100</td>
                            <td>±2%</td>
                            <td>50 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-orange"></span> Orange</td>
                            <td>3</td>
                            <td>×1K</td>
                            <td>-</td>
                            <td>15 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-yellow"></span> Yellow</td>
                            <td>4</td>
                            <td>×10K</td>
                            <td>-</td>
                            <td>25 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-green"></span> Green</td>
                            <td>5</td>
                            <td>×100K</td>
                            <td>±0.5%</td>
                            <td>20 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-blue"></span> Blue</td>
                            <td>6</td>
                            <td>×1M</td>
                            <td>±0.25%</td>
                            <td>10 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-violet"></span> Violet</td>
                            <td>7</td>
                            <td>×10M</td>
                            <td>±0.1%</td>
                            <td>5 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-grey"></span> Grey</td>
                            <td>8</td>
                            <td>×100M</td>
                            <td>±0.05%</td>
                            <td>1 ppm/K</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-white"></span> White</td>
                            <td>9</td>
                            <td>×1G</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-gold"></span> Gold</td>
                            <td>-</td>
                            <td>×0.1</td>
                            <td>±5%</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td><span class="table-color-sample color-silver"></span> Silver</td>
                            <td>-</td>
                            <td>×0.01</td>
                            <td>±10%</td>
                            <td>-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="content-section">
            <h2>How to Read Resistor Color Codes</h2>
            <p>Resistor color codes use colored bands to indicate the resistance value, tolerance, and sometimes temperature coefficient. The number of bands determines the precision and additional specifications.</p>
            
            <h3>4-Band Resistors</h3>
            <p>The most common type with two significant digits, a multiplier, and tolerance band.</p>
            
            <div class="highlight-box">
                <h3>4-Band Reading Order</h3>
                <ul>
                    <li><strong>Band 1:</strong> First significant digit</li>
                    <li><strong>Band 2:</strong> Second significant digit</li>
                    <li><strong>Band 3:</strong> Multiplier (power of 10)</li>
                    <li><strong>Band 4:</strong> Tolerance</li>
                </ul>
            </div>
            
            <h3>5-Band Resistors</h3>
            <p>Higher precision resistors with three significant digits, multiplier, and tolerance.</p>
            
            <div class="highlight-box">
                <h3>5-Band Reading Order</h3>
                <ul>
                    <li><strong>Band 1:</strong> First significant digit</li>
                    <li><strong>Band 2:</strong> Second significant digit</li>
                    <li><strong>Band 3:</strong> Third significant digit</li>
                    <li><strong>Band 4:</strong> Multiplier (power of 10)</li>
                    <li><strong>Band 5:</strong> Tolerance</li>
                </ul>
            </div>
            
            <h3>6-Band Resistors</h3>
            <p>Precision resistors that include temperature coefficient information.</p>
            
            <div class="highlight-box">
                <h3>6-Band Reading Order</h3>
                <ul>
                    <li><strong>Band 1:</strong> First significant digit</li>
                    <li><strong>Band 2:</strong> Second significant digit</li>
                    <li><strong>Band 3:</strong> Third significant digit</li>
                    <li><strong>Band 4:</strong> Multiplier (power of 10)</li>
                    <li><strong>Band 5:</strong> Tolerance</li>
                    <li><strong>Band 6:</strong> Temperature coefficient</li>
                </ul>
            </div>
        </div>

        <div class="content-section">
            <h2>Tips for Reading Resistors</h2>
            <ul>
                <li>Always read from the end with bands closer together</li>
                <li>The tolerance band is usually gold or silver and set apart</li>
                <li>If unsure of orientation, try both directions and see which makes sense</li>
                <li>Use a multimeter to verify your reading</li>
                <li>Consider the context - very high or low values may indicate incorrect reading</li>
            </ul>
            
            <div class="highlight-box">
                <h3>Common Tolerance Values</h3>
                <ul>
                    <li><strong>Gold (±5%):</strong> Standard tolerance for general purpose</li>
                    <li><strong>Silver (±10%):</strong> Lower precision, older resistors</li>
                    <li><strong>Brown (±1%):</strong> Precision resistors</li>
                    <li><strong>Red (±2%):</strong> Good precision</li>
                    <li><strong>Green (±0.5%):</strong> High precision</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        (function() {
            // Color data
            const colorData = {
                black: { digit: 0, multiplier: 1, color: '#000000' },
                brown: { digit: 1, multiplier: 10, tolerance: 1, tempCoeff: 100, color: '#8B4513' },
                red: { digit: 2, multiplier: 100, tolerance: 2, tempCoeff: 50, color: '#FF0000' },
                orange: { digit: 3, multiplier: 1000, tempCoeff: 15, color: '#FFA500' },
                yellow: { digit: 4, multiplier: 10000, tempCoeff: 25, color: '#FFFF00' },
                green: { digit: 5, multiplier: 100000, tolerance: 0.5, tempCoeff: 20, color: '#008000' },
                blue: { digit: 6, multiplier: 1000000, tolerance: 0.25, tempCoeff: 10, color: '#0000FF' },
                violet: { digit: 7, multiplier: 10000000, tolerance: 0.1, tempCoeff: 5, color: '#8A2BE2' },
                grey: { digit: 8, multiplier: 100000000, tolerance: 0.05, tempCoeff: 1, color: '#808080' },
                white: { digit: 9, multiplier: 1000000000, color: '#FFFFFF' },
                gold: { multiplier: 0.1, tolerance: 5, color: '#FFD700' },
                silver: { multiplier: 0.01, tolerance: 10, color: '#C0C0C0' }
            };

            // DOM elements
            const bandBtns = document.querySelectorAll('.band-btn');
            const colorSelectors = document.getElementById('colorSelectors');
            const calculateBtn = document.getElementById('calculate');
            const resetBtn = document.getElementById('reset');
            const resultsContent = document.getElementById('results-content');

            let currentBands = 5;
            let selectedColors = {
                band1: 'black',
                band2: 'black', 
                band3: 'black',
                band4: 'black',
                band5: 'gold',
                band6: 'brown'
            };

            // Initialize
            init();

            function init() {
                setupBandSelector();
                setupEventListeners();
                updateBandDisplay();
                generateColorSelectors();
                updateVisualBands();
            }

            function setupBandSelector() {
                bandBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        bandBtns.forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        currentBands = parseInt(btn.dataset.bands);
                        updateBandDisplay();
                        generateColorSelectors();
                        updateVisualBands();
                        calculate();
                    });
                });
            }

            function setupEventListeners() {
                calculateBtn.addEventListener('click', calculate);
                resetBtn.addEventListener('click', reset);
            }

            function updateBandDisplay() {
                const bands = document.querySelectorAll('.color-band');
                bands.forEach((band, index) => {
                    if (index < currentBands) {
                        band.style.display = 'block';
                    } else {
                        band.style.display = 'none';
                    }
                });
            }

            function generateColorSelectors() {
                colorSelectors.innerHTML = '';
                
                for (let i = 1; i <= currentBands; i++) {
                    const group = document.createElement('div');
                    group.className = 'color-group';
                    
                    const label = document.createElement('label');
                    label.textContent = getBandLabel(i);
                    
                    const options = document.createElement('div');
                    options.className = 'color-options';
                    
                    const availableColors = getAvailableColors(i);
                    availableColors.forEach(colorName => {
                        const option = document.createElement('div');
                        option.className = `color-option color-${colorName}`;
                        option.dataset.color = colorName;
                        option.dataset.band = `band${i}`;
                        
                        if (selectedColors[`band${i}`] === colorName) {
                            option.classList.add('selected');
                        }
                        
                        option.addEventListener('click', selectColor);
                        options.appendChild(option);
                    });
                    
                    group.appendChild(label);
                    group.appendChild(options);
                    colorSelectors.appendChild(group);
                }
            }

            function getBandLabel(bandNumber) {
                if (currentBands === 4) {
                    const labels = ['', '1st Digit', '2nd Digit', 'Multiplier', 'Tolerance'];
                    return labels[bandNumber];
                } else if (currentBands === 5) {
                    const labels = ['', '1st Digit', '2nd Digit', '3rd Digit', 'Multiplier', 'Tolerance'];
                    return labels[bandNumber];
                } else if (currentBands === 6) {
                    const labels = ['', '1st Digit', '2nd Digit', '3rd Digit', 'Multiplier', 'Tolerance', 'Temp. Coeff.'];
                    return labels[bandNumber];
                }
            }

            function getAvailableColors(bandNumber) {
                const digitColors = ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey', 'white'];
                const multiplierColors = ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey', 'white', 'gold', 'silver'];
                const toleranceColors = ['brown', 'red', 'green', 'blue', 'violet', 'grey', 'gold', 'silver'];
                const tempCoeffColors = ['brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey'];

                if (currentBands === 4) {
                    if (bandNumber <= 2) return digitColors;
                    if (bandNumber === 3) return multiplierColors;
                    if (bandNumber === 4) return toleranceColors;
                } else if (currentBands === 5) {
                    if (bandNumber <= 3) return digitColors;
                    if (bandNumber === 4) return multiplierColors;
                    if (bandNumber === 5) return toleranceColors;
                } else if (currentBands === 6) {
                    if (bandNumber <= 3) return digitColors;
                    if (bandNumber === 4) return multiplierColors;
                    if (bandNumber === 5) return toleranceColors;
                    if (bandNumber === 6) return tempCoeffColors;
                }
                return digitColors;
            }

            function selectColor(event) {
                const option = event.target;
                const color = option.dataset.color;
                const band = option.dataset.band;
                
                // Remove previous selection in this group
                const group = option.parentElement;
                group.querySelector
function selectColor(event) {
                const option = event.target;
                const color = option.dataset.color;
                const band = option.dataset.band;
                
                // Remove previous selection in this group
                const group = option.parentElement;
                group.querySelectorAll('.color-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add selection to clicked option
                option.classList.add('selected');
                
                // Update selected colors
                selectedColors[band] = color;
                
                // Update visual band
                updateVisualBands();
                
                // Auto-calculate
                calculate();
            }

            function updateVisualBands() {
                for (let i = 1; i <= 6; i++) {
                    const band = document.getElementById(`band${i}`);
                    if (band && selectedColors[`band${i}`]) {
                        const color = selectedColors[`band${i}`];
                        band.style.backgroundColor = colorData[color].color;
                        band.dataset.color = color;
                        
                        // Add border for white color visibility
                        if (color === 'white') {
                            band.style.border = '2px solid #ccc';
                        } else {
                            band.style.border = '1px solid rgba(0,0,0,0.2)';
                        }
                    }
                }
            }

            function calculate() {
                try {
                    let resistance = 0;
                    let tolerance = '';
                    let tempCoeff = '';
                    
                    if (currentBands === 4) {
                        // 4-band: digit1 + digit2 + multiplier + tolerance
                        const digit1 = colorData[selectedColors.band1].digit;
                        const digit2 = colorData[selectedColors.band2].digit;
                        const multiplier = colorData[selectedColors.band3].multiplier;
                        const toleranceColor = selectedColors.band4;
                        
                        if (digit1 === undefined || digit2 === undefined || multiplier === undefined) {
                            throw new Error('Invalid color selection for digits or multiplier');
                        }
                        
                        resistance = (digit1 * 10 + digit2) * multiplier;
                        tolerance = colorData[toleranceColor].tolerance ? `±${colorData[toleranceColor].tolerance}%` : 'N/A';
                        
                    } else if (currentBands === 5) {
                        // 5-band: digit1 + digit2 + digit3 + multiplier + tolerance
                        const digit1 = colorData[selectedColors.band1].digit;
                        const digit2 = colorData[selectedColors.band2].digit;
                        const digit3 = colorData[selectedColors.band3].digit;
                        const multiplier = colorData[selectedColors.band4].multiplier;
                        const toleranceColor = selectedColors.band5;
                        
                        if (digit1 === undefined || digit2 === undefined || digit3 === undefined || multiplier === undefined) {
                            throw new Error('Invalid color selection for digits or multiplier');
                        }
                        
                        resistance = (digit1 * 100 + digit2 * 10 + digit3) * multiplier;
                        tolerance = colorData[toleranceColor].tolerance ? `±${colorData[toleranceColor].tolerance}%` : 'N/A';
                        
                    } else if (currentBands === 6) {
                        // 6-band: digit1 + digit2 + digit3 + multiplier + tolerance + tempCoeff
                        const digit1 = colorData[selectedColors.band1].digit;
                        const digit2 = colorData[selectedColors.band2].digit;
                        const digit3 = colorData[selectedColors.band3].digit;
                        const multiplier = colorData[selectedColors.band4].multiplier;
                        const toleranceColor = selectedColors.band5;
                        const tempCoeffColor = selectedColors.band6;
                        
                        if (digit1 === undefined || digit2 === undefined || digit3 === undefined || multiplier === undefined) {
                            throw new Error('Invalid color selection for digits or multiplier');
                        }
                        
                        resistance = (digit1 * 100 + digit2 * 10 + digit3) * multiplier;
                        tolerance = colorData[toleranceColor].tolerance ? `±${colorData[toleranceColor].tolerance}%` : 'N/A';
                        tempCoeff = colorData[tempCoeffColor].tempCoeff ? `${colorData[tempCoeffColor].tempCoeff} ppm/K` : 'N/A';
                    }
                    
                    displayResults(resistance, tolerance, tempCoeff);
                    
                } catch (error) {
                    resultsContent.innerHTML = `<p style="color: #e74c3c;">Error: ${error.message}</p>`;
                }
            }

            function displayResults(resistance, tolerance, tempCoeff) {
                const formattedResistance = formatResistance(resistance);
                const resistanceRange = calculateResistanceRange(resistance, tolerance);
                
                let html = `
                    <ul>
                        <li><strong>Resistance:</strong> ${formattedResistance}</li>
                        <li><strong>Tolerance:</strong> ${tolerance}</li>
                `;
                
                if (resistanceRange) {
                    html += `<li><strong>Range:</strong> ${resistanceRange}</li>`;
                }
                
                if (tempCoeff && currentBands === 6) {
                    html += `<li><strong>Temperature Coefficient:</strong> ${tempCoeff}</li>`;
                }
                
                html += '</ul>';
                resultsContent.innerHTML = html;
            }

            function formatResistance(resistance) {
                if (resistance >= 1000000000) {
                    return `${(resistance / 1000000000).toFixed(2)} GΩ`;
                } else if (resistance >= 1000000) {
                    return `${(resistance / 1000000).toFixed(2)} MΩ`;
                } else if (resistance >= 1000) {
                    return `${(resistance / 1000).toFixed(2)} kΩ`;
                } else {
                    return `${resistance.toFixed(2)} Ω`;
                }
            }

            function calculateResistanceRange(resistance, toleranceStr) {
                if (!toleranceStr || toleranceStr === 'N/A') return null;
                
                const toleranceMatch = toleranceStr.match(/±([0-9.]+)%/);
                if (!toleranceMatch) return null;
                
                const tolerancePercent = parseFloat(toleranceMatch[1]);
                const toleranceValue = resistance * (tolerancePercent / 100);
                const minResistance = resistance - toleranceValue;
                const maxResistance = resistance + toleranceValue;
                
                return `${formatResistance(minResistance)} - ${formatResistance(maxResistance)}`;
            }

            function reset() {
                // Reset to default colors
                selectedColors = {
                    band1: 'brown',
                    band2: 'black',
                    band3: 'black',
                    band4: 'black',
                    band5: 'gold',
                    band6: 'brown'
                };
                
                // Update visual bands
                updateVisualBands();
                
                // Regenerate color selectors to update selections
                generateColorSelectors();
                
                // Calculate with new values
                calculate();
            }

            // Color band click handler for direct selection
            document.querySelectorAll('.color-band').forEach(band => {
                band.addEventListener('click', function() {
                    const bandId = this.id;
                    const bandNumber = bandId.replace('band', '');
                    
                    // Scroll to the corresponding color selector
                    const colorGroups = document.querySelectorAll('.color-group');
                    if (colorGroups[bandNumber - 1]) {
                        colorGroups[bandNumber - 1].scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'center' 
                        });
                        
                        // Add a brief highlight effect
                        const group = colorGroups[bandNumber - 1];
                        group.style.backgroundColor = '#e3f2fd';
                        setTimeout(() => {
                            group.style.backgroundColor = '';
                        }, 1000);
                    }
                });
            });

        })();
    </script>
</body>
</html>