<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free spring calculator for compression, extension, and torsion springs. Calculate spring rate, force, deflection, and stress analysis.">
    <style>
        .spring-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .spring-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .spring-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .spring-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .spring-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }
        
        .spring-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .spring-type-btn {
            padding: 12px 24px;
            border: 2px solid #3498db;
            background: #fff;
            color: #3498db;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .spring-type-btn.active, .spring-type-btn:hover {
            background: #3498db;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .calculator {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .calc-section {
            margin-bottom: 25px;
        }

        .calc-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .input-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .input-field label {
            font-weight: 500;
            color: #2c3e50;
            font-size: 1rem;
        }

        .input-field input, .input-field select {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: #fff;
        }

        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
        }
        
        .spring-visual {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 1px solid #e1e8ed;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .spring-diagram {
            font-size: 3rem;
            color: #3498db;
            margin: 25px 0;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
        }

        .btn {
            padding: 14px 25px;
            border: none;
            background: #3498db;
            color: #fff;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: #e74c3c;
            color: #fff;
        }

        .btn-secondary:hover {
            background: #c0392b;
        }

        .results {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e1e8ed;
            margin-top: 20px;
            display: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .result-item {
            background: #f8f9fa;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 1rem;
            color: #2c3e50;
        }
        
        .content-section {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-top: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .content-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .content-section h3 {
            color: #2c3e50;
            margin: 20px 0 15px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .content-section p {
            margin-bottom: 15px;
            color: #5a6c7d;
            line-height: 1.7;
        }

        .content-section ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .content-section li {
            margin-bottom: 8px;
            color: #5a6c7d;
            line-height: 1.6;
        }

        .highlight-box {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .formula-box {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .spring-calc {
                padding: 20px;
            }

            .spring-calc h1 {
                font-size: 1.8rem;
            }

            .calculator, .content-section {
                padding: 20px;
            }

            .input-group {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .spring-types {
                gap: 8px;
            }

            .spring-type-btn {
                padding: 10px 18px;
                font-size: 0.95rem;
            }

            .btn-group {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .spring-calc {
                padding: 15px;
            }

            .spring-calc h1 {
                font-size: 1.6rem;
            }

            .calculator, .content-section, .results {
                padding: 15px;
            }

            .input-field input, .input-field select {
                padding: 10px 12px;
                font-size: 1rem;
            }

            .btn {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .result-item {
                padding: 10px 12px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="spring-calc">
        <header>
            <h1>Spring Calculator</h1>
            <p class="intro">Calculate spring parameters including spring rate, force, deflection, stress, and natural frequency for compression, extension, and torsion springs.</p>
        </header>

        <div class="spring-types">
            <button class="spring-type-btn active" onclick="selectSpringType('compression')">Compression Spring</button>
            <button class="spring-type-btn" onclick="selectSpringType('extension')">Extension Spring</button>
            <button class="spring-type-btn" onclick="selectSpringType('torsion')">Torsion Spring</button>
        </div>

        <div class="spring-visual">
            <h3 id="spring-title">Compression Spring</h3>
            <div class="spring-diagram" id="spring-diagram">🌀</div>
            <p id="spring-description">Helical spring compressed under axial load</p>
        </div>

        <div class="calculator">
            <div class="calc-section">
                <h3>Spring Geometry</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="wireDiameter">Wire Diameter (d) - mm</label>
                        <input type="number" id="wireDiameter" step="any" placeholder="Enter wire diameter">
                    </div>
                    <div class="input-field">
                        <label for="meanDiameter">Mean Coil Diameter (D) - mm</label>
                        <input type="number" id="meanDiameter" step="any" placeholder="Enter mean diameter">
                    </div>
                    <div class="input-field">
                        <label for="activeCoils">Number of Active Coils (n)</label>
                        <input type="number" id="activeCoils" step="any" placeholder="Enter active coils">
                    </div>
                </div>
            </div>
            
            <div class="calc-section">
                <h3>Material Properties</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="shearModulus">Shear Modulus (G) - GPa</label>
                        <input type="number" id="shearModulus" step="any" value="80" placeholder="Enter shear modulus">
                    </div>
                    <div class="input-field">
                        <label for="materialType">Material Type</label>
                        <select id="materialType" onchange="updateMaterial()">
                            <option value="custom">Custom</option>
                            <option value="steel">Music Wire (Steel)</option>
                            <option value="stainless">Stainless Steel</option>
                            <option value="phosphor">Phosphor Bronze</option>
                            <option value="beryllium">Beryllium Copper</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="calc-section">
                <h3>Loading Conditions</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="appliedForce">Applied Force (F) - N</label>
                        <input type="number" id="appliedForce" step="any" placeholder="Enter force">
                    </div>
                    <div class="input-field">
                        <label for="deflection">Deflection (δ) - mm</label>
                        <input type="number" id="deflection" step="any" placeholder="Enter deflection">
                    </div>
                    <div class="input-field" id="torsion-field" style="display: none;">
                        <label for="torque">Applied Torque (T) - N⋅m</label>
                        <input type="number" id="torque" step="any" placeholder="Enter torque">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Spring Design Formulas</h2>
            <p>The calculator uses standard spring design formulas based on spring type and loading conditions:</p>
            
            <div class="highlight-box">
                <h3>Compression/Extension Springs</h3>
                <div class="formula-box">
                    Spring Rate: k = Gd⁴/(8D³n)<br>
                    Force: F = k × δ<br>
                    Shear Stress: τ = 8FD/(πd³) × K<br>
                    Spring Index: C = D/d<br>
                    Wahl Factor: K = (4C-1)/(4C-4) + 0.615/C
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Torsion Springs</h3>
                <div class="formula-box">
                    Spring Rate: k = Ed⁴/(64DR)<br>
                    Torque: T = k × θ<br>
                    Bending Stress: σ = 32T/(πd³) × K<br>
                    Where R = mean radius, θ = angular deflection
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Material Properties (Typical Values)</h3>
                <p><strong>Music Wire:</strong> G = 80 GPa, High strength</p>
                <p><strong>Stainless Steel:</strong> G = 75 GPa, Corrosion resistant</p>
                <p><strong>Phosphor Bronze:</strong> G = 40 GPa, Good conductivity</p>
                <p><strong>Beryllium Copper:</strong> G = 48 GPa, Non-magnetic</p>
            </div>
        </div>
    </div>

    <script>
        let currentSpringType = 'compression';
        
        const materialProperties = {
            steel: { G: 80, name: 'Music Wire (Steel)' },
            stainless: { G: 75, name: 'Stainless Steel' },
            phosphor: { G: 40, name: 'Phosphor Bronze' },
            beryllium: { G: 48, name: 'Beryllium Copper' }
        };
        
        function selectSpringType(type) {
            currentSpringType = type;
            
            // Update active button
            document.querySelectorAll('.spring-type-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update visual and descriptions
            const titles = {
                compression: 'Compression Spring',
                extension: 'Extension Spring',
                torsion: 'Torsion Spring'
            };
            
            const diagrams = {
                compression: '🌀',
                extension: '🔗',
                torsion: '↻'
            };
            
            const descriptions = {
                compression: 'Helical spring compressed under axial load',
                extension: 'Helical spring extended under axial load',
                torsion: 'Helical spring twisted under torque load'
            };
            
            document.getElementById('spring-title').textContent = titles[type];
            document.getElementById('spring-diagram').textContent = diagrams[type];
            document.getElementById('spring-description').textContent = descriptions[type];
            
            // Show/hide torsion-specific fields
            document.getElementById('torsion-field').style.display = type === 'torsion' ? 'flex' : 'none';
        }
        
        function updateMaterial() {
            const material = document.getElementById('materialType').value;
            if (material !== 'custom' && materialProperties[material]) {
                document.getElementById('shearModulus').value = materialProperties[material].G;
            }
        }
        
        function calculate() {
            const d = parseFloat(document.getElementById('wireDiameter').value) / 1000; // Convert mm to m
            const D = parseFloat(document.getElementById('meanDiameter').value) / 1000; // Convert mm to m
            const n = parseFloat(document.getElementById('activeCoils').value);
            const G = parseFloat(document.getElementById('shearModulus').value) * 1e9; // Convert GPa to Pa
            const F = parseFloat(document.getElementById('appliedForce').value);
            const delta = parseFloat(document.getElementById('deflection').value) / 1000; // Convert mm to m
            const T = parseFloat(document.getElementById('torque').value);
            
            if (!d || !D || !n || !G) {
                alert('Please enter spring geometry and material properties');
                return;
            }
            
            let results = [];
            
            // Common calculations
            const C = D / d; // Spring index
            const wahlFactor = (4 * C - 1) / (4 * C - 4) + 0.615 / C;
            
            results.push(`<div class="result-item"><strong>Spring Index (C):</strong> ${C.toFixed(2)}</div>`);
            results.push(`<div class="result-item"><strong>Wahl Correction Factor:</strong> ${wahlFactor.toFixed(3)}</div>`);
            
            if (currentSpringType === 'compression' || currentSpringType === 'extension') {
                // Spring rate calculation
                const k = (G * Math.pow(d, 4)) / (8 * Math.pow(D, 3) * n);
                results.push(`<div class="result-item"><strong>Spring Rate (k):</strong> ${formatValue(k)} N/m</div>`);
                
                // Force and deflection calculations
                if (F && !delta) {
                    const calculatedDelta = F / k;
                    results.push(`<div class="result-item"><strong>Deflection:</strong> ${(calculatedDelta * 1000).toFixed(3)} mm</div>`);
                } else if (delta && !F) {
                    const calculatedF = k * delta;
                    results.push(`<div class="result-item"><strong>Force:</strong> ${calculatedF.toFixed(2)} N</div>`);
                } else if (F && delta) {
                    results.push(`<div class="result-item"><strong>Given Force:</strong> ${F} N</div>`);
                    results.push(`<div class="result-item"><strong>Given Deflection:</strong> ${delta * 1000} mm</div>`);
                }
                
                // Stress calculation
                const force = F || (delta ? k * delta : 0);
                if (force) {
                    const shearStress = (8 * force * D) / (Math.PI * Math.pow(d, 3)) * wahlFactor;
                    results.push(`<div class="result-item"><strong>Maximum Shear Stress:</strong> ${formatValue(shearStress)} Pa</div>`);
                    
                    // Safety factor (assuming allowable stress of 0.5 * ultimate strength)
                    const allowableStress = 1000e6; // Typical for music wire
                    const safetyFactor = allowableStress / shearStress;
                    results.push(`<div class="result-item"><strong>Safety Factor:</strong> ${safetyFactor.toFixed(2)}</div>`);
                }
                
                // Natural frequency (approximate)
                const density = 7850; // Steel density kg/m³
                const mass = density * Math.PI * Math.pow(d/2, 2) * Math.PI * D * n;
                const naturalFreq = (1 / (2 * Math.PI)) * Math.sqrt(k / mass);
                results.push(`<div class="result-item"><strong>Natural Frequency:</strong> ${naturalFreq.toFixed(1)} Hz</div>`);
                
            } else if (currentSpringType === 'torsion') {
                // Torsion spring calculations
                const E = G * 2.6; // Approximate relationship for steel
                const R = D / 2;
                const k_torsion = (E * Math.pow(d, 4)) / (64 * D * R);
                
                results.push(`<div class="result-item"><strong>Torsional Spring Rate:</strong> ${formatValue(k_torsion)} N⋅m/rad</div>`);
                
                if (T) {
                    const theta = T / k_torsion;
                    results.push(`<div class="result-item"><strong>Angular Deflection:</strong> ${(theta * 180 / Math.PI).toFixed(2)}°</div>`);
                    
                    const bendingStress = (32 * T) / (Math.PI * Math.pow(d, 3)) * wahlFactor;
                    results.push(`<div class="result-item"><strong>Maximum Bending Stress:</strong> ${formatValue(bendingStress)} Pa</div>`);
                }
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (Math.abs(value) >= 1e9) return (value / 1e9).toFixed(2) + 'G';
            if (Math.abs(value) >= 1e6) return (value / 1e6).toFixed(2) + 'M';
            if (Math.abs(value) >= 1e3) return (value / 1e3).toFixed(2) + 'k';
            if (Math.abs(value) >= 1) return value.toFixed(3);
            if (Math.abs(value) >= 1e-3) return (value * 1e3).toFixed(2) + 'm';
            if (Math.abs(value) >= 1e-6) return (value * 1e6).toFixed(2) + 'μ';
            return (value * 1e9).toFixed(2) + 'n';
        }
        
        function reset() {
            document.getElementById('wireDiameter').value = '';
            document.getElementById('meanDiameter').value = '';
            document.getElementById('activeCoils').value = '';
            document.getElementById('shearModulus').value = '80';
            document.getElementById('materialType').selectedIndex = 0;
            document.getElementById('appliedForce').value = '';
            document.getElementById('deflection').value = '';
            document.getElementById('torque').value = '';
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
