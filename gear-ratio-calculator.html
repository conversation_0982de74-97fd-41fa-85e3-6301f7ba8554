<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gear Ratio Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free gear ratio calculator for mechanical engineering. Calculate gear ratios, speed reduction, torque multiplication, and gear train analysis.">
    <style>
        .gear-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .gear-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .gear-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .gear-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .gear-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .gear-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .gear-type-btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .gear-type-btn.active {
            background: #007cba;
            color: #fff;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .gear-visual {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .gear-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .gear {
            width: 80px;
            height: 80px;
            border: 3px solid #007cba;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #007cba;
            position: relative;
        }
        
        .gear.large {
            width: 120px;
            height: 120px;
        }
        
        .gear::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px dashed #007cba;
            opacity: 0.3;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .formula-box {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .gear-calc {
                padding: 15px;
            }
            
            .gear-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
            
            .gear-types {
                gap: 5px;
            }
            
            .gear-type-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
            
            .gear-diagram {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="gear-calc">
        <header>
            <h1>Gear Ratio Calculator</h1>
            <p class="intro">Calculate gear ratios, speed reduction, torque multiplication, and mechanical advantage for gear systems. Essential tool for mechanical engineering design.</p>
        </header>

        <div class="gear-types">
            <button class="gear-type-btn active" onclick="selectGearType('simple')">Simple Gear Pair</button>
            <button class="gear-type-btn" onclick="selectGearType('compound')">Compound Gear Train</button>
            <button class="gear-type-btn" onclick="selectGearType('planetary')">Planetary Gear</button>
        </div>

        <div class="gear-visual">
            <h3 id="gear-title">Simple Gear Pair</h3>
            <div class="gear-diagram" id="gear-diagram">
                <div class="gear">
                    <span>D</span>
                </div>
                <div style="font-size: 1.5rem; color: #007cba;">⚙</div>
                <div class="gear large">
                    <span>G</span>
                </div>
            </div>
            <p><strong>D:</strong> Driver (Input) &nbsp;&nbsp; <strong>G:</strong> Driven (Output)</p>
        </div>

        <div class="calculator">
            <div id="simple-inputs">
                <div class="calc-section">
                    <h3>Gear Specifications</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="driverTeeth">Driver Teeth (Input)</label>
                            <input type="number" id="driverTeeth" step="1" placeholder="Enter driver teeth">
                        </div>
                        <div class="input-field">
                            <label for="drivenTeeth">Driven Teeth (Output)</label>
                            <input type="number" id="drivenTeeth" step="1" placeholder="Enter driven teeth">
                        </div>
                        <div class="input-field">
                            <label for="inputSpeed">Input Speed (RPM)</label>
                            <input type="number" id="inputSpeed" step="any" placeholder="Enter input speed">
                        </div>
                    </div>
                </div>
                
                <div class="calc-section">
                    <h3>Power & Torque</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="inputTorque">Input Torque (N⋅m)</label>
                            <input type="number" id="inputTorque" step="any" placeholder="Enter input torque">
                        </div>
                        <div class="input-field">
                            <label for="efficiency">Efficiency (%)</label>
                            <input type="number" id="efficiency" step="any" value="95" placeholder="Enter efficiency">
                        </div>
                    </div>
                </div>
            </div>

            <div id="compound-inputs" style="display: none;">
                <div class="calc-section">
                    <h3>Compound Gear Train</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="stage1Driver">Stage 1 - Driver Teeth</label>
                            <input type="number" id="stage1Driver" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="stage1Driven">Stage 1 - Driven Teeth</label>
                            <input type="number" id="stage1Driven" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="stage2Driver">Stage 2 - Driver Teeth</label>
                            <input type="number" id="stage2Driver" step="1" placeholder="Enter teeth">
                        </div>
                    </div>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="stage2Driven">Stage 2 - Driven Teeth</label>
                            <input type="number" id="stage2Driven" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="compoundInputSpeed">Input Speed (RPM)</label>
                            <input type="number" id="compoundInputSpeed" step="any" placeholder="Enter speed">
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Gear Ratio Fundamentals</h2>
            <p>Gear ratios determine the relationship between input and output speed, torque, and mechanical advantage in gear systems.</p>
            
            <div class="highlight-box">
                <h3>Basic Formulas</h3>
                <div class="formula-box">
                    Gear Ratio = Driven Teeth / Driver Teeth<br>
                    Speed Ratio = Driver Speed / Driven Speed<br>
                    Torque Ratio = Driven Torque / Driver Torque<br>
                    Mechanical Advantage = Output Torque / Input Torque
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Key Relationships</h3>
                <p><strong>Speed Reduction:</strong> When driven gear is larger, output speed decreases</p>
                <p><strong>Torque Multiplication:</strong> Speed reduction increases torque proportionally</p>
                <p><strong>Power Conservation:</strong> Input power ≈ Output power (minus losses)</p>
            </div>
        </div>
    </div>

    <script>
        let currentGearType = 'simple';
        
        function selectGearType(type) {
            currentGearType = type;
            
            // Update active button
            document.querySelectorAll('.gear-type-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide input sections
            document.getElementById('simple-inputs').style.display = type === 'simple' ? 'block' : 'none';
            document.getElementById('compound-inputs').style.display = type === 'compound' ? 'block' : 'none';
            
            // Update titles
            const titles = {
                'simple': 'Simple Gear Pair',
                'compound': 'Compound Gear Train',
                'planetary': 'Planetary Gear System'
            };
            
            document.getElementById('gear-title').textContent = titles[type];
            
            if (type === 'planetary') {
                alert('Planetary gear calculator coming soon! Please use Simple or Compound gear options.');
                // Reset to simple
                document.querySelector('.gear-type-btn').click();
            }
        }
        
        function calculate() {
            let results = [];
            
            if (currentGearType === 'simple') {
                const driverTeeth = parseFloat(document.getElementById('driverTeeth').value);
                const drivenTeeth = parseFloat(document.getElementById('drivenTeeth').value);
                const inputSpeed = parseFloat(document.getElementById('inputSpeed').value);
                const inputTorque = parseFloat(document.getElementById('inputTorque').value);
                const efficiency = parseFloat(document.getElementById('efficiency').value) || 95;
                
                if (!driverTeeth || !drivenTeeth) {
                    alert('Please enter the number of teeth for both gears');
                    return;
                }
                
                const gearRatio = drivenTeeth / driverTeeth;
                const speedRatio = driverTeeth / drivenTeeth;
                
                results.push(`<div class="result-item"><strong>Gear Ratio:</strong> ${gearRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Speed Ratio:</strong> ${speedRatio.toFixed(3)}:1</div>`);
                
                if (gearRatio > 1) {
                    results.push(`<div class="result-item"><strong>Type:</strong> Speed Reduction (${gearRatio.toFixed(1)}x slower)</div>`);
                    results.push(`<div class="result-item"><strong>Torque Multiplication:</strong> ${gearRatio.toFixed(1)}x</div>`);
                } else {
                    results.push(`<div class="result-item"><strong>Type:</strong> Speed Increase (${(1/gearRatio).toFixed(1)}x faster)</div>`);
                    results.push(`<div class="result-item"><strong>Torque Reduction:</strong> ${(1/gearRatio).toFixed(1)}x</div>`);
                }
                
                if (inputSpeed) {
                    const outputSpeed = inputSpeed * speedRatio;
                    results.push(`<div class="result-item"><strong>Output Speed:</strong> ${outputSpeed.toFixed(2)} RPM</div>`);
                }
                
                if (inputTorque) {
                    const outputTorque = inputTorque * gearRatio * (efficiency / 100);
                    results.push(`<div class="result-item"><strong>Output Torque:</strong> ${outputTorque.toFixed(2)} N⋅m</div>`);
                    results.push(`<div class="result-item"><strong>Mechanical Advantage:</strong> ${(outputTorque/inputTorque).toFixed(2)}</div>`);
                }
                
            } else if (currentGearType === 'compound') {
                const s1Driver = parseFloat(document.getElementById('stage1Driver').value);
                const s1Driven = parseFloat(document.getElementById('stage1Driven').value);
                const s2Driver = parseFloat(document.getElementById('stage2Driver').value);
                const s2Driven = parseFloat(document.getElementById('stage2Driven').value);
                const inputSpeed = parseFloat(document.getElementById('compoundInputSpeed').value);
                
                if (!s1Driver || !s1Driven || !s2Driver || !s2Driven) {
                    alert('Please enter all gear teeth values');
                    return;
                }
                
                const stage1Ratio = s1Driven / s1Driver;
                const stage2Ratio = s2Driven / s2Driver;
                const overallRatio = stage1Ratio * stage2Ratio;
                const overallSpeedRatio = 1 / overallRatio;
                
                results.push(`<div class="result-item"><strong>Stage 1 Ratio:</strong> ${stage1Ratio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Stage 2 Ratio:</strong> ${stage2Ratio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Overall Gear Ratio:</strong> ${overallRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Overall Speed Ratio:</strong> ${overallSpeedRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Total Torque Multiplication:</strong> ${overallRatio.toFixed(1)}x</div>`);
                
                if (inputSpeed) {
                    const outputSpeed = inputSpeed * overallSpeedRatio;
                    results.push(`<div class="result-item"><strong>Final Output Speed:</strong> ${outputSpeed.toFixed(2)} RPM</div>`);
                }
            }
            
            if (results.length === 0) {
                alert('Please enter valid values for calculation');
                return;
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function reset() {
            // Reset simple gear inputs
            document.getElementById('driverTeeth').value = '';
            document.getElementById('drivenTeeth').value = '';
            document.getElementById('inputSpeed').value = '';
            document.getElementById('inputTorque').value = '';
            document.getElementById('efficiency').value = '95';
            
            // Reset compound gear inputs
            document.getElementById('stage1Driver').value = '';
            document.getElementById('stage1Driven').value = '';
            document.getElementById('stage2Driver').value = '';
            document.getElementById('stage2Driven').value = '';
            document.getElementById('compoundInputSpeed').value = '';
            
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
