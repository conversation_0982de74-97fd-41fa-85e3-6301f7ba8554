<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gear Ratio Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free gear ratio calculator for mechanical engineering. Calculate gear ratios, speed reduction, torque multiplication, and gear train analysis.">
    <style>
        .gear-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .gear-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .gear-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .gear-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .gear-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }
        
        .gear-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .gear-type-btn {
            padding: 12px 24px;
            border: 2px solid #3498db;
            background: #fff;
            color: #3498db;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .gear-type-btn.active, .gear-type-btn:hover {
            background: #3498db;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .calculator {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .calc-section {
            margin-bottom: 25px;
        }

        .calc-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .input-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .input-field label {
            font-weight: 500;
            color: #2c3e50;
            font-size: 1rem;
        }

        .input-field input, .input-field select {
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: #fff;
        }

        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
        }
        
        .gear-visual {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 1px solid #e1e8ed;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .gear-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            margin: 25px 0;
        }

        .gear {
            width: 80px;
            height: 80px;
            border: 3px solid #3498db;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #3498db;
            position: relative;
        }

        .gear.large {
            width: 120px;
            height: 120px;
        }

        .gear::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px dashed #3498db;
            opacity: 0.3;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
        }

        .btn {
            padding: 14px 25px;
            border: none;
            background: #3498db;
            color: #fff;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: #e74c3c;
            color: #fff;
        }
        
        .btn-secondary:hover {
            background: #c0392b;
        }

        .results {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e1e8ed;
            margin-top: 20px;
            display: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .result-item {
            background: #f8f9fa;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 1rem;
            color: #2c3e50;
        }

        .content-section {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-top: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .content-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .content-section h3 {
            color: #2c3e50;
            margin: 20px 0 15px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .content-section p {
            margin-bottom: 15px;
            color: #5a6c7d;
            line-height: 1.7;
        }

        .content-section ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .content-section li {
            margin-bottom: 8px;
            color: #5a6c7d;
            line-height: 1.6;
        }

        .highlight-box {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .formula-box {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .gear-calc {
                padding: 20px;
            }

            .gear-calc h1 {
                font-size: 1.8rem;
            }

            .calculator, .content-section {
                padding: 20px;
            }

            .input-group {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .gear-types {
                gap: 8px;
            }

            .gear-type-btn {
                padding: 10px 18px;
                font-size: 0.95rem;
            }

            .btn-group {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
            }

            .gear-diagram {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .gear-calc {
                padding: 15px;
            }

            .gear-calc h1 {
                font-size: 1.6rem;
            }

            .calculator, .content-section, .results {
                padding: 15px;
            }

            .input-field input, .input-field select {
                padding: 10px 12px;
                font-size: 1rem;
            }

            .btn {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .result-item {
                padding: 10px 12px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="gear-calc">
        <header>
            <h1>Gear Ratio Calculator</h1>
            <p class="intro">Calculate gear ratios, speed reduction, torque multiplication, and mechanical advantage for gear systems. Essential tool for mechanical engineering design.</p>
        </header>

        <div class="gear-types">
            <button class="gear-type-btn active" onclick="selectGearType('simple')">Simple Gear Pair</button>
            <button class="gear-type-btn" onclick="selectGearType('compound')">Compound Gear Train</button>
            <button class="gear-type-btn" onclick="selectGearType('planetary')">Planetary Gear</button>
        </div>

        <div class="gear-visual">
            <h3 id="gear-title">Simple Gear Pair</h3>
            <div class="gear-diagram" id="gear-diagram">
                <div class="gear">
                    <span>D</span>
                </div>
                <div style="font-size: 1.5rem; color: #3498db;">⚙</div>
                <div class="gear large">
                    <span>G</span>
                </div>
            </div>
            <p><strong>D:</strong> Driver (Input) &nbsp;&nbsp; <strong>G:</strong> Driven (Output)</p>
        </div>

        <div class="calculator">
            <div id="simple-inputs">
                <div class="calc-section">
                    <h3>Gear Specifications</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="driverTeeth">Driver Teeth (Input)</label>
                            <input type="number" id="driverTeeth" step="1" placeholder="Enter driver teeth">
                        </div>
                        <div class="input-field">
                            <label for="drivenTeeth">Driven Teeth (Output)</label>
                            <input type="number" id="drivenTeeth" step="1" placeholder="Enter driven teeth">
                        </div>
                        <div class="input-field">
                            <label for="inputSpeed">Input Speed (RPM)</label>
                            <input type="number" id="inputSpeed" step="any" placeholder="Enter input speed">
                        </div>
                    </div>
                </div>
                
                <div class="calc-section">
                    <h3>Power & Torque</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="inputTorque">Input Torque (N⋅m)</label>
                            <input type="number" id="inputTorque" step="any" placeholder="Enter input torque">
                        </div>
                        <div class="input-field">
                            <label for="efficiency">Efficiency (%)</label>
                            <input type="number" id="efficiency" step="any" value="95" placeholder="Enter efficiency">
                        </div>
                    </div>
                </div>
            </div>

            <div id="compound-inputs" style="display: none;">
                <div class="calc-section">
                    <h3>Compound Gear Train</h3>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="stage1Driver">Stage 1 - Driver Teeth</label>
                            <input type="number" id="stage1Driver" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="stage1Driven">Stage 1 - Driven Teeth</label>
                            <input type="number" id="stage1Driven" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="stage2Driver">Stage 2 - Driver Teeth</label>
                            <input type="number" id="stage2Driver" step="1" placeholder="Enter teeth">
                        </div>
                    </div>
                    <div class="input-group">
                        <div class="input-field">
                            <label for="stage2Driven">Stage 2 - Driven Teeth</label>
                            <input type="number" id="stage2Driven" step="1" placeholder="Enter teeth">
                        </div>
                        <div class="input-field">
                            <label for="compoundInputSpeed">Input Speed (RPM)</label>
                            <input type="number" id="compoundInputSpeed" step="any" placeholder="Enter speed">
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Gear Ratio Fundamentals</h2>
            <p>Gear ratios determine the relationship between input and output speed, torque, and mechanical advantage in gear systems.</p>
            
            <div class="highlight-box">
                <h3>Basic Formulas</h3>
                <div class="formula-box">
                    Gear Ratio = Driven Teeth / Driver Teeth<br>
                    Speed Ratio = Driver Speed / Driven Speed<br>
                    Torque Ratio = Driven Torque / Driver Torque<br>
                    Mechanical Advantage = Output Torque / Input Torque
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Key Relationships</h3>
                <p><strong>Speed Reduction:</strong> When driven gear is larger, output speed decreases</p>
                <p><strong>Torque Multiplication:</strong> Speed reduction increases torque proportionally</p>
                <p><strong>Power Conservation:</strong> Input power ≈ Output power (minus losses)</p>
            </div>
        </div>
    </div>

    <script>
        let currentGearType = 'simple';
        
        function selectGearType(type) {
            currentGearType = type;
            
            // Update active button
            document.querySelectorAll('.gear-type-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide input sections
            document.getElementById('simple-inputs').style.display = type === 'simple' ? 'block' : 'none';
            document.getElementById('compound-inputs').style.display = type === 'compound' ? 'block' : 'none';
            
            // Update titles
            const titles = {
                'simple': 'Simple Gear Pair',
                'compound': 'Compound Gear Train',
                'planetary': 'Planetary Gear System'
            };
            
            document.getElementById('gear-title').textContent = titles[type];
            
            if (type === 'planetary') {
                alert('Planetary gear calculator coming soon! Please use Simple or Compound gear options.');
                // Reset to simple
                document.querySelector('.gear-type-btn').click();
            }
        }
        
        function calculate() {
            let results = [];
            
            if (currentGearType === 'simple') {
                const driverTeeth = parseFloat(document.getElementById('driverTeeth').value);
                const drivenTeeth = parseFloat(document.getElementById('drivenTeeth').value);
                const inputSpeed = parseFloat(document.getElementById('inputSpeed').value);
                const inputTorque = parseFloat(document.getElementById('inputTorque').value);
                const efficiency = parseFloat(document.getElementById('efficiency').value) || 95;
                
                if (!driverTeeth || !drivenTeeth) {
                    alert('Please enter the number of teeth for both gears');
                    return;
                }
                
                const gearRatio = drivenTeeth / driverTeeth;
                const speedRatio = driverTeeth / drivenTeeth;
                
                results.push(`<div class="result-item"><strong>Gear Ratio:</strong> ${gearRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Speed Ratio:</strong> ${speedRatio.toFixed(3)}:1</div>`);
                
                if (gearRatio > 1) {
                    results.push(`<div class="result-item"><strong>Type:</strong> Speed Reduction (${gearRatio.toFixed(1)}x slower)</div>`);
                    results.push(`<div class="result-item"><strong>Torque Multiplication:</strong> ${gearRatio.toFixed(1)}x</div>`);
                } else {
                    results.push(`<div class="result-item"><strong>Type:</strong> Speed Increase (${(1/gearRatio).toFixed(1)}x faster)</div>`);
                    results.push(`<div class="result-item"><strong>Torque Reduction:</strong> ${(1/gearRatio).toFixed(1)}x</div>`);
                }
                
                if (inputSpeed) {
                    const outputSpeed = inputSpeed * speedRatio;
                    results.push(`<div class="result-item"><strong>Output Speed:</strong> ${outputSpeed.toFixed(2)} RPM</div>`);
                }
                
                if (inputTorque) {
                    const outputTorque = inputTorque * gearRatio * (efficiency / 100);
                    results.push(`<div class="result-item"><strong>Output Torque:</strong> ${outputTorque.toFixed(2)} N⋅m</div>`);
                    results.push(`<div class="result-item"><strong>Mechanical Advantage:</strong> ${(outputTorque/inputTorque).toFixed(2)}</div>`);
                }
                
            } else if (currentGearType === 'compound') {
                const s1Driver = parseFloat(document.getElementById('stage1Driver').value);
                const s1Driven = parseFloat(document.getElementById('stage1Driven').value);
                const s2Driver = parseFloat(document.getElementById('stage2Driver').value);
                const s2Driven = parseFloat(document.getElementById('stage2Driven').value);
                const inputSpeed = parseFloat(document.getElementById('compoundInputSpeed').value);
                
                if (!s1Driver || !s1Driven || !s2Driver || !s2Driven) {
                    alert('Please enter all gear teeth values');
                    return;
                }
                
                const stage1Ratio = s1Driven / s1Driver;
                const stage2Ratio = s2Driven / s2Driver;
                const overallRatio = stage1Ratio * stage2Ratio;
                const overallSpeedRatio = 1 / overallRatio;
                
                results.push(`<div class="result-item"><strong>Stage 1 Ratio:</strong> ${stage1Ratio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Stage 2 Ratio:</strong> ${stage2Ratio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Overall Gear Ratio:</strong> ${overallRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Overall Speed Ratio:</strong> ${overallSpeedRatio.toFixed(3)}:1</div>`);
                results.push(`<div class="result-item"><strong>Total Torque Multiplication:</strong> ${overallRatio.toFixed(1)}x</div>`);
                
                if (inputSpeed) {
                    const outputSpeed = inputSpeed * overallSpeedRatio;
                    results.push(`<div class="result-item"><strong>Final Output Speed:</strong> ${outputSpeed.toFixed(2)} RPM</div>`);
                }
            }
            
            if (results.length === 0) {
                alert('Please enter valid values for calculation');
                return;
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function reset() {
            // Reset simple gear inputs
            document.getElementById('driverTeeth').value = '';
            document.getElementById('drivenTeeth').value = '';
            document.getElementById('inputSpeed').value = '';
            document.getElementById('inputTorque').value = '';
            document.getElementById('efficiency').value = '95';
            
            // Reset compound gear inputs
            document.getElementById('stage1Driver').value = '';
            document.getElementById('stage1Driven').value = '';
            document.getElementById('stage2Driver').value = '';
            document.getElementById('stage2Driven').value = '';
            document.getElementById('compoundInputSpeed').value = '';
            
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
