<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capacitor Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free capacitor calculator for capacitance, reactance, energy, and charge calculations. Essential tool for electronics engineers and students.">
    <style>
        .capacitor-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .capacitor-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .capacitor-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .capacitor-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .capacitor-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }

        .calculator {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1.05rem;
        }

        .input-field input, .input-field select {
            padding: 12px 15px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1.1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            background: #fff;
        }

        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
        }

        .btn {
            padding: 14px 25px;
            border: none;
            background: #3498db;
            color: #fff;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn.reset {
            background: #e74c3c;
        }

        .btn.reset:hover {
            background: #c0392b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e1e8ed;
            margin-top: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .results h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .result-item {
            padding: 12px 16px;
            background-color: #f0f6fa;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 1.1rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        .content-section {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-top: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .content-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
            border-bottom: 1px solid #e1e8ed;
            padding-bottom: 10px;
        }

        .content-section h3 {
            color: #2c3e50;
            margin: 20px 0 10px;
            font-size: 1.25rem;
        }

        .content-section p {
            margin-bottom: 15px;
            color: #5a6c7d;
            line-height: 1.7;
        }

        .content-section strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .highlight-box {
            background: #edf7ff;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .highlight-box h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .capacitor-calc {
                padding: 20px;
            }

            .capacitor-calc h1 {
                font-size: 1.8rem;
            }

            .calculator, .content-section {
                padding: 20px;
            }

            .input-group {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .btn-group {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .capacitor-calc {
                padding: 15px;
            }

            .capacitor-calc h1 {
                font-size: 1.6rem;
            }

            .calculator, .content-section, .results {
                padding: 15px;
            }

            .input-field input, .input-field select {
                padding: 10px 12px;
                font-size: 1rem;
            }

            .btn {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .result-item {
                padding: 10px 12px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="capacitor-calc">
        <header>
            <h1>Capacitor Calculator</h1>
            <p class="intro">Calculate capacitance, reactance, energy storage, and charge for capacitors. Essential tool for electronics design and analysis.</p>
        </header>

        <div class="calculator">
            <div class="calc-section">
                <h3>Capacitance & Reactance Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="capacitance">Capacitance</label>
                        <input type="number" id="capacitance" step="any" placeholder="Enter capacitance">
                    </div>
                    <div class="input-field">
                        <label for="capUnit">Unit</label>
                        <select id="capUnit">
                            <option value="1e-6">µF (microfarad)</option>
                            <option value="1e-9">nF (nanofarad)</option>
                            <option value="1e-12">pF (picofarad)</option>
                            <option value="1">F (farad)</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="number" id="frequency" step="any" placeholder="Enter frequency">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Energy & Charge Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="voltage">Voltage (V)</label>
                        <input type="number" id="voltage" step="any" placeholder="Enter voltage">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>About Capacitors</h2>
            <p>Capacitors are passive electronic components that store electrical energy in an electric field. They consist of two conductive plates separated by an insulating material called a dielectric.</p>
            
            <div class="highlight-box">
                <h3>Key Formulas</h3>
                <p><strong>Capacitive Reactance:</strong> Xc = 1/(2πfC)</p>
                <p><strong>Energy Storage:</strong> E = ½CV²</p>
                <p><strong>Charge Storage:</strong> Q = CV</p>
            </div>
        </div>
    </div>

    <script>
        function calculate() {
            const capacitance = parseFloat(document.getElementById('capacitance').value);
            const capUnit = parseFloat(document.getElementById('capUnit').value);
            const frequency = parseFloat(document.getElementById('frequency').value);
            const voltage = parseFloat(document.getElementById('voltage').value);
            
            if (!capacitance || capacitance <= 0) {
                alert('Please enter a valid capacitance value');
                return;
            }
            
            const capInFarads = capacitance * capUnit;
            let results = [];
            
            // Calculate reactance if frequency is provided
            if (frequency && frequency > 0) {
                const reactance = 1 / (2 * Math.PI * frequency * capInFarads);
                results.push(`<div class="result-item"><strong>Capacitive Reactance:</strong> ${formatValue(reactance)} Ω</div>`);
            }
            
            // Calculate energy and charge if voltage is provided
            if (voltage && voltage > 0) {
                const energy = 0.5 * capInFarads * voltage * voltage;
                const charge = capInFarads * voltage;
                results.push(`<div class="result-item"><strong>Energy Stored:</strong> ${formatValue(energy)} J</div>`);
                results.push(`<div class="result-item"><strong>Charge Stored:</strong> ${formatValue(charge)} C</div>`);
            }
            
            results.push(`<div class="result-item"><strong>Capacitance:</strong> ${formatCapacitance(capInFarads)}</div>`);
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (value >= 1e6) return (value / 1e6).toFixed(3) + 'M';
            if (value >= 1e3) return (value / 1e3).toFixed(3) + 'k';
            if (value >= 1) return value.toFixed(3);
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + 'm';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + 'µ';
            if (value >= 1e-9) return (value * 1e9).toFixed(3) + 'n';
            return (value * 1e12).toFixed(3) + 'p';
        }
        
        function formatCapacitance(value) {
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + ' mF';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + ' µF';
            if (value >= 1e-9) return (value * 1e9).toFixed(3) + ' nF';
            return (value * 1e12).toFixed(3) + ' pF';
        }
        
        function reset() {
            document.getElementById('capacitance').value = '';
            document.getElementById('frequency').value = '';
            document.getElementById('voltage').value = '';
            document.getElementById('capUnit').selectedIndex = 0;
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
