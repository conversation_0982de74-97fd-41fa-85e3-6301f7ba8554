<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capacitor Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free capacitor calculator for capacitance, reactance, energy, and charge calculations. Essential tool for electronics engineers and students.">
    <style>
        .capacitor-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .capacitor-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .capacitor-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .capacitor-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .capacitor-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        @media (max-width: 768px) {
            .capacitor-calc {
                padding: 15px;
            }
            
            .capacitor-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="capacitor-calc">
        <header>
            <h1>Capacitor Calculator</h1>
            <p class="intro">Calculate capacitance, reactance, energy storage, and charge for capacitors. Essential tool for electronics design and analysis.</p>
        </header>

        <div class="calculator">
            <div class="calc-section">
                <h3>Capacitance & Reactance Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="capacitance">Capacitance</label>
                        <input type="number" id="capacitance" step="any" placeholder="Enter capacitance">
                    </div>
                    <div class="input-field">
                        <label for="capUnit">Unit</label>
                        <select id="capUnit">
                            <option value="1e-6">µF (microfarad)</option>
                            <option value="1e-9">nF (nanofarad)</option>
                            <option value="1e-12">pF (picofarad)</option>
                            <option value="1">F (farad)</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="number" id="frequency" step="any" placeholder="Enter frequency">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Energy & Charge Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="voltage">Voltage (V)</label>
                        <input type="number" id="voltage" step="any" placeholder="Enter voltage">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>About Capacitors</h2>
            <p>Capacitors are passive electronic components that store electrical energy in an electric field. They consist of two conductive plates separated by an insulating material called a dielectric.</p>
            
            <div class="highlight-box">
                <h3>Key Formulas</h3>
                <p><strong>Capacitive Reactance:</strong> Xc = 1/(2πfC)</p>
                <p><strong>Energy Storage:</strong> E = ½CV²</p>
                <p><strong>Charge Storage:</strong> Q = CV</p>
            </div>
        </div>
    </div>

    <script>
        function calculate() {
            const capacitance = parseFloat(document.getElementById('capacitance').value);
            const capUnit = parseFloat(document.getElementById('capUnit').value);
            const frequency = parseFloat(document.getElementById('frequency').value);
            const voltage = parseFloat(document.getElementById('voltage').value);
            
            if (!capacitance || capacitance <= 0) {
                alert('Please enter a valid capacitance value');
                return;
            }
            
            const capInFarads = capacitance * capUnit;
            let results = [];
            
            // Calculate reactance if frequency is provided
            if (frequency && frequency > 0) {
                const reactance = 1 / (2 * Math.PI * frequency * capInFarads);
                results.push(`<div class="result-item"><strong>Capacitive Reactance:</strong> ${formatValue(reactance)} Ω</div>`);
            }
            
            // Calculate energy and charge if voltage is provided
            if (voltage && voltage > 0) {
                const energy = 0.5 * capInFarads * voltage * voltage;
                const charge = capInFarads * voltage;
                results.push(`<div class="result-item"><strong>Energy Stored:</strong> ${formatValue(energy)} J</div>`);
                results.push(`<div class="result-item"><strong>Charge Stored:</strong> ${formatValue(charge)} C</div>`);
            }
            
            results.push(`<div class="result-item"><strong>Capacitance:</strong> ${formatCapacitance(capInFarads)}</div>`);
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (value >= 1e6) return (value / 1e6).toFixed(3) + 'M';
            if (value >= 1e3) return (value / 1e3).toFixed(3) + 'k';
            if (value >= 1) return value.toFixed(3);
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + 'm';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + 'µ';
            if (value >= 1e-9) return (value * 1e9).toFixed(3) + 'n';
            return (value * 1e12).toFixed(3) + 'p';
        }
        
        function formatCapacitance(value) {
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + ' mF';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + ' µF';
            if (value >= 1e-9) return (value * 1e9).toFixed(3) + ' nF';
            return (value * 1e12).toFixed(3) + ' pF';
        }
        
        function reset() {
            document.getElementById('capacitance').value = '';
            document.getElementById('frequency').value = '';
            document.getElementById('voltage').value = '';
            document.getElementById('capUnit').selectedIndex = 0;
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
