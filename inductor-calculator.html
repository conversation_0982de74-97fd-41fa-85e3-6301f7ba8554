<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inductor Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free inductor calculator for inductance, reactance, energy, and coil design calculations. Essential tool for electronics engineers and students.">
    <style>
        .inductor-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .inductor-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #fff;
            padding: 30px;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .inductor-calc header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e1e8ed;
        }

        .inductor-calc h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.2rem;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .inductor-calc .intro {
            font-size: 1.1rem;
            color: #5a6c7d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }

        .calculator {
            background: #f7fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .calc-section {
            margin-bottom: 25px;
        }
        
        .calc-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
        }

        .btn {
            padding: 14px 25px;
            border: none;
            background: #3498db;
            color: #fff;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: #e74c3c;
        }

        .btn-secondary:hover {
            background: #c0392b;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        @media (max-width: 768px) {
            .inductor-calc {
                padding: 20px;
            }

            .inductor-calc h1 {
                font-size: 1.8rem;
            }

            .calculator, .content-section {
                padding: 20px;
            }

            .input-group {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .btn-group {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .inductor-calc {
                padding: 15px;
            }

            .inductor-calc h1 {
                font-size: 1.6rem;
            }

            .calculator, .content-section, .results {
                padding: 15px;
            }

            .input-field input, .input-field select {
                padding: 10px 12px;
                font-size: 1rem;
            }

            .btn {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .result-item {
                padding: 10px 12px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="inductor-calc">
        <header>
            <h1>Inductor Calculator</h1>
            <p class="intro">Calculate inductance, reactance, energy storage, and coil parameters for inductors. Essential tool for electronics design and analysis.</p>
        </header>

        <div class="calculator">
            <div class="calc-section">
                <h3>Inductance & Reactance Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="inductance">Inductance</label>
                        <input type="number" id="inductance" step="any" placeholder="Enter inductance">
                    </div>
                    <div class="input-field">
                        <label for="indUnit">Unit</label>
                        <select id="indUnit">
                            <option value="1e-3">mH (millihenry)</option>
                            <option value="1e-6">µH (microhenry)</option>
                            <option value="1e-9">nH (nanohenry)</option>
                            <option value="1">H (henry)</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="number" id="frequency" step="any" placeholder="Enter frequency">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Energy & Current Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="current">Current (A)</label>
                        <input type="number" id="current" step="any" placeholder="Enter current">
                    </div>
                </div>
            </div>

            <div class="calc-section">
                <h3>Coil Design Calculator</h3>
                <div class="input-group">
                    <div class="input-field">
                        <label for="turns">Number of Turns</label>
                        <input type="number" id="turns" step="1" placeholder="Enter turns">
                    </div>
                    <div class="input-field">
                        <label for="diameter">Coil Diameter (mm)</label>
                        <input type="number" id="diameter" step="any" placeholder="Enter diameter">
                    </div>
                    <div class="input-field">
                        <label for="length">Coil Length (mm)</label>
                        <input type="number" id="length" step="any" placeholder="Enter length">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>About Inductors</h2>
            <p>Inductors are passive electronic components that store energy in a magnetic field when electric current flows through them. They oppose changes in current flow and are essential in filtering, energy storage, and oscillator circuits.</p>
            
            <div class="highlight-box">
                <h3>Key Formulas</h3>
                <p><strong>Inductive Reactance:</strong> XL = 2πfL</p>
                <p><strong>Energy Storage:</strong> E = ½LI²</p>
                <p><strong>Air Core Inductance:</strong> L = (μ₀N²A)/l</p>
            </div>
        </div>
    </div>

    <script>
        function calculate() {
            const inductance = parseFloat(document.getElementById('inductance').value);
            const indUnit = parseFloat(document.getElementById('indUnit').value);
            const frequency = parseFloat(document.getElementById('frequency').value);
            const current = parseFloat(document.getElementById('current').value);
            const turns = parseFloat(document.getElementById('turns').value);
            const diameter = parseFloat(document.getElementById('diameter').value);
            const length = parseFloat(document.getElementById('length').value);
            
            let results = [];
            
            if (inductance && inductance > 0) {
                const indInHenries = inductance * indUnit;
                results.push(`<div class="result-item"><strong>Inductance:</strong> ${formatInductance(indInHenries)}</div>`);
                
                // Calculate reactance if frequency is provided
                if (frequency && frequency > 0) {
                    const reactance = 2 * Math.PI * frequency * indInHenries;
                    results.push(`<div class="result-item"><strong>Inductive Reactance:</strong> ${formatValue(reactance)} Ω</div>`);
                }
                
                // Calculate energy if current is provided
                if (current && current > 0) {
                    const energy = 0.5 * indInHenries * current * current;
                    results.push(`<div class="result-item"><strong>Energy Stored:</strong> ${formatValue(energy)} J</div>`);
                }
            }
            
            // Calculate coil inductance if parameters are provided
            if (turns && diameter && length && turns > 0 && diameter > 0 && length > 0) {
                const radius = (diameter / 2) / 1000; // Convert mm to m
                const lengthM = length / 1000; // Convert mm to m
                const area = Math.PI * radius * radius;
                const mu0 = 4 * Math.PI * 1e-7; // Permeability of free space
                const coilInductance = (mu0 * turns * turns * area) / lengthM;
                results.push(`<div class="result-item"><strong>Calculated Coil Inductance:</strong> ${formatInductance(coilInductance)}</div>`);
            }
            
            if (results.length === 0) {
                alert('Please enter valid values for calculation');
                return;
            }
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (value >= 1e6) return (value / 1e6).toFixed(3) + 'M';
            if (value >= 1e3) return (value / 1e3).toFixed(3) + 'k';
            if (value >= 1) return value.toFixed(3);
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + 'm';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + 'µ';
            return (value * 1e9).toFixed(3) + 'n';
        }
        
        function formatInductance(value) {
            if (value >= 1) return value.toFixed(3) + ' H';
            if (value >= 1e-3) return (value * 1e3).toFixed(3) + ' mH';
            if (value >= 1e-6) return (value * 1e6).toFixed(3) + ' µH';
            return (value * 1e9).toFixed(3) + ' nH';
        }
        
        function reset() {
            document.getElementById('inductance').value = '';
            document.getElementById('frequency').value = '';
            document.getElementById('current').value = '';
            document.getElementById('turns').value = '';
            document.getElementById('diameter').value = '';
            document.getElementById('length').value = '';
            document.getElementById('indUnit').selectedIndex = 0;
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
