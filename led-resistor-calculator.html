<!-- LED Resistor Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .led-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
    
    .led-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }
    
    /* Header styles */
    .led-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }
    
    .led-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .led-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }
    
    /* Calculator section */
    .led-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .led-calc .input-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .led-calc .input-field {
        display: flex;
        flex-direction: column;
    }
    
    .led-calc .input-field label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }
    
    .led-calc .input-with-unit {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .led-calc .input-with-unit input {
        flex: 3;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
    }
    
    .led-calc .input-with-unit input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .led-calc .input-with-unit select {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        min-width: 60px;
        max-width: 80px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
        cursor: pointer;
    }
    
    .led-calc .input-with-unit select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .led-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .led-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }
    
    .led-calc .calculate {
        background-color: #3498db;
        color: white;
    }
    
    .led-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .led-calc .reset {
        background-color: #e74c3c;
        color: white;
    }
    
    .led-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Results section */
    .led-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }
    
    .led-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }
    
    .led-calc .results ul {
        list-style: none;
    }
    
    .led-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }
    
    /* Formula section */
    .led-calc .formulas {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .led-calc .formulas h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
    }
    
    .led-calc .formula-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .led-calc .formula-item {
        text-align: center;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .led-calc .formula-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
    
    .led-calc .formula-item h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .led-calc .formula-item p {
        font-size: 1rem;
        color: #555;
    }
    
    /* Content section */
    .led-calc .content-section {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-top: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .led-calc .content-section h2 {
        color: #2c3e50;
        margin-bottom: 18px;
        font-size: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }
    
    .led-calc .content-section h3 {
        color: #2c3e50;
        margin: 20px 0 10px;
        font-size: 1.25rem;
    }
    
    .led-calc .content-section p {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.7;
    }
    
    .led-calc .content-section strong {
        color: #2c3e50;
        font-weight: 600;
    }
    
    .led-calc .content-section ol,
    .led-calc .content-section ul {
        margin-bottom: 20px;
        padding-left: 25px;
    }
    
    .led-calc .content-section li {
        margin-bottom: 8px;
        color: #555;
        line-height: 1.7;
    }
    
    /* Ad placement spacers */
    .led-calc .ad-spacer {
        height: 15px;
        width: 100%;
        margin: 25px 0;
        text-align: center;
        clear: both;
    }
    
    /* Highlight box for important content */
    .led-calc .highlight-box {
        background-color: #edf7ff;
        border-left: 4px solid #3498db;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }
    
    .led-calc .highlight-box h3 {
        margin-top: 0;
        color: #2c3e50;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .led-calc {
            padding: 15px;
            margin: 0 auto;
            border-radius: 8px;
        }
    
        .led-calc h1 {
            font-size: 1.5rem;
        }
        
        .led-calc .calculator,
        .led-calc .formulas,
        .led-calc .content-section {
            padding: 15px;
        }
    
        .led-calc .input-group {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .led-calc .formula-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    
        .led-calc .input-field label {
            font-size: 1rem;
            margin-bottom: 8px;
        }
        
        .led-calc .input-with-unit input {
            flex: 2;
        }
        
        .led-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
        }
        
        .led-calc .btn {
            font-size: 1rem;
            padding: 12px;
            height: 44px;
        }
        
        .led-calc .results li {
            font-size: 1rem;
            padding: 10px;
        }
        
        .led-calc .formula-item h3 {
            font-size: 1.1rem;
        }
        
        .led-calc .formula-item p {
            font-size: 0.9rem;
        }
        
        .led-calc .content-section {
            padding: 20px;
        }
        
        .led-calc .content-section h2 {
            font-size: 1.3rem;
        }
        
        .led-calc .content-section h3 {
            font-size: 1.15rem;
        }
    }
    
    @media (max-width: 480px) {
        .led-calc {
            padding: 12px;
        }
        
        .led-calc .input-with-unit {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
        
        .led-calc .input-with-unit input {
            min-height: 44px;
            flex: 3;
        }
        
        .led-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
            min-height: 44px;
        }
        
        .led-calc .formula-item {
            padding: 12px;
        }
        
        .led-calc .results li {
            padding: 10px;
        }
    }
    
    @media (max-width: 350px) {
        .led-calc .input-with-unit {
            flex-direction: column;
            gap: 6px;
        }
        
        .led-calc .input-with-unit select {
            width: 100%;
        }
        
        .led-calc .button-group {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

<div class="led-calc">
    <header>
        <h1>LED Resistor Calculator</h1>
        <p class="intro">Calculate the appropriate resistor value for your LED circuit based on the LED's specifications and power supply voltage.</p>
    </header>

    <div class="calculator">
        <div class="input-group">
            <div class="input-field">
                <label for="supply-voltage">Supply Voltage (V)</label>
                <div class="input-with-unit">
                    <input type="number" id="supply-voltage" placeholder="Enter supply voltage">
                    <select id="supply-voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="led-voltage">LED Forward Voltage (V)</label>
                <div class="input-with-unit">
                    <input type="number" id="led-voltage" placeholder="Enter LED voltage">
                    <select id="led-voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="led-current">LED Forward Current (I)</label>
                <div class="input-with-unit">
                    <input type="number" id="led-current" placeholder="Enter LED current">
                    <select id="led-current-unit">
                        <option value="A">A</option>
                        <option value="mA">mA</option>
                        <option value="µA">µA</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="num-leds">Number of LEDs</label>
                <div class="input-with-unit">
                    <input type="number" id="num-leds" placeholder="Enter number of LEDs" value="1" min="1">
                </div>
            </div>
        </div>
        <div class="button-group">
            <button id="calculate" class="btn calculate">Calculate</button>
            <button id="reset" class="btn reset">Reset</button>
        </div>
        <div class="results" id="results">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <div class="formulas">
        <h2>LED Resistor Formulas</h2>
        <div class="formula-grid">
            <div class="formula-item">
                <h3>R = (V<sub>s</sub> - V<sub>LED</sub>) / I</h3>
                <p>Resistor = (Supply Voltage - LED Voltage) / LED Current</p>
            </div>
            <div class="formula-item">
                <h3>P = I² × R</h3>
                <p>Resistor Power = Current² × Resistance</p>
            </div>
            <div class="formula-item">
                <h3>V<sub>R</sub> = V<sub>s</sub> - V<sub>LED</sub></h3>
                <p>Resistor Voltage = Supply Voltage - LED Voltage</p>
            </div>
            <div class="formula-item">
                <h3>I = (V<sub>s</sub> - V<sub>LED</sub>) / R</h3>
                <p>LED Current = (Supply Voltage - LED Voltage) / Resistance</p>
            </div>
        </div>
    </div>

    <div class="content-section">
        <h2>Understanding LED Circuits</h2>
        <p>LEDs (Light Emitting Diodes) require a specific forward voltage and current to operate properly. To prevent damage to the LED, a current-limiting resistor is necessary. This calculator helps you determine the appropriate resistor value for your LED circuit.</p>
        
        <div class="highlight-box">
            <h3>Key Concept</h3>
            <p>The resistor value is calculated using Ohm's Law, where R = (V<sub>s</sub> - V<sub>LED</sub>) / I. This ensures the LED receives the correct current while limiting the voltage across it.</p>
        </div>
        
        <h2>How to Use the Calculator</h2>
        <ol>
            <li>Enter your power supply voltage</li>
            <li>Enter the LED's forward voltage (typically 1.8V-3.3V for most LEDs)</li>
            <li>Enter the desired LED current (usually 20mA for standard LEDs)</li>
            <li>Enter the number of LEDs in series</li>
            <li>Click "Calculate" to find the required resistor value and power rating</li>
        </ol>

        <h2>Common LED Specifications</h2>
        <ul>
            <li><strong>Red LED:</strong> 1.8V - 2.2V forward voltage</li>
            <li><strong>Green LED:</strong> 2.8V - 3.4V forward voltage</li>
            <li><strong>Blue LED:</strong> 3.0V - 3.4V forward voltage</li>
            <li><strong>White LED:</strong> 3.0V - 3.4V forward voltage</li>
            <li><strong>Standard Current:</strong> 20mA (0.02A)</li>
        </ul>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>LED Circuit Safety Tips</h2>
        <p>When working with LED circuits, follow these safety guidelines:</p>
        
        <ul>
            <li><strong>Always use a current-limiting resistor</strong> to prevent LED damage</li>
            <li><strong>Check LED polarity</strong> - LEDs only work in one direction</li>
            <li><strong>Use appropriate power supply</strong> - ensure voltage matches your circuit</li>
            <li><strong>Consider heat dissipation</strong> - choose resistors with adequate power ratings</li>
            <li><strong>Test connections</strong> before applying power</li>
            <li><strong>Use proper soldering techniques</strong> to avoid heat damage</li>
        </ul>
        
        <div class="highlight-box">
            <h3>Safety Warning</h3>
            <p>Never connect an LED directly to a power supply without a current-limiting resistor. This will result in immediate damage to the LED.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Series vs. Parallel LED Connections</h2>
        
        <h3>Series Connection</h3>
        <p>When LEDs are connected in series:</p>
        <ul>
            <li>Forward voltages add up</li>
            <li>Current remains the same</li>
            <li>One resistor can be used for multiple LEDs</li>
            <li>If one LED fails, the entire string goes out</li>
        </ul>
        
        <h3>Parallel Connection</h3>
        <p>When LEDs are connected in parallel:</p>
        <ul>
            <li>Forward voltage remains the same</li>
            <li>Currents add up</li>
            <li>Each LED needs its own resistor</li>
            <li>If one LED fails, others continue working</li>
        </ul>
        
        <div class="highlight-box">
            <h3>Best Practice</h3>
            <p>For most applications, series connection is preferred as it's more efficient and requires fewer components. However, parallel connection might be better when you need independent control of each LED.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Frequently Asked Questions</h2>
        
        <h3>Why do LEDs need resistors?</h3>
        <p>LEDs are current-driven devices that require a specific current to operate properly. Without a resistor, they would draw too much current and burn out. The resistor limits the current to a safe value.</p>
        
        <h3>What happens if I use the wrong resistor value?</h3>
        <p>Using a resistor that's too small will allow too much current to flow, potentially damaging the LED. Using a resistor that's too large will result in dim or no light output.</p>
        
        <h3>Can I use any resistor for my LED?</h3>
        <p>While any resistor with the correct value will work, you should choose one with an appropriate power rating to handle the heat generated. Standard 1/4W resistors are usually sufficient for single LEDs.</p>
        
        <h3>What's the difference between forward voltage and supply voltage?</h3>
        <p>Forward voltage is the voltage required for the LED to conduct and emit light. Supply voltage is the voltage of your power source. The resistor drops the difference between these voltages.</p>
        
        <div class="highlight-box">
            <h3>Common Mistake</h3>
            <p>One common error is forgetting to account for the number of LEDs in series when calculating the resistor value. The forward voltage of each LED must be subtracted from the supply voltage.</p>
        </div>
    </div>
</div>

<script>
(function() {
    // Get DOM elements
    const supplyVoltageInput = document.getElementById('supply-voltage');
    const ledVoltageInput = document.getElementById('led-voltage');
    const ledCurrentInput = document.getElementById('led-current');
    const numLedsInput = document.getElementById('num-leds');
    const supplyVoltageUnit = document.getElementById('supply-voltage-unit');
    const ledVoltageUnit = document.getElementById('led-voltage-unit');
    const ledCurrentUnit = document.getElementById('led-current-unit');
    const calculateBtn = document.getElementById('calculate');
    const resetBtn = document.getElementById('reset');
    const resultsContent = document.getElementById('results-content');

    // Add event listeners
    calculateBtn.addEventListener('click', calculate);
    resetBtn.addEventListener('click', reset);

    // Add keyboard support - enter key triggers calculation
    document.querySelectorAll('.led-calc input').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculate();
                e.preventDefault();
            }
        });
    });

    // Unit conversion factors
    const unitFactors = {
        voltage: {
            'V': 1,
            'mV': 0.001
        },
        current: {
            'A': 1,
            'mA': 0.001,
            'µA': 0.000001
        }
    };

    // Function to convert value to base unit
    function convertToBase(value, unit, type) {
        return value * unitFactors[type][unit];
    }

    // Function to convert from base unit to target unit
    function convertFromBase(value, unit, type) {
        return value / unitFactors[type][unit];
    }

    // Function to validate inputs
    function validateInputs() {
        const inputs = [supplyVoltageInput, ledVoltageInput, ledCurrentInput, numLedsInput];
        for (let input of inputs) {
            if (input.value === '' || isNaN(input.value) || parseFloat(input.value) <= 0) {
                return false;
            }
        }
        return true;
    }

    // Format number with appropriate decimal places
    function formatNumber(num) {
        // Handle very small numbers
        if (num < 0.01 && num > 0) {
            return num.toExponential(2);
        }
        // Handle very large numbers
        if (num > 1000000) {
            return num.toExponential(2);
        }
        // Regular numbers
        return num.toFixed(2);
    }

    // Function to calculate resistor value
    function calculate() {
        // Clear previous results
        resultsContent.innerHTML = '';
        
        // Validate inputs
        if (!validateInputs()) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter valid positive numbers for all fields.</p>';
            return;
        }

        // Get input values and convert to base units
        const vs = convertToBase(parseFloat(supplyVoltageInput.value), supplyVoltageUnit.value, 'voltage');
        const vled = convertToBase(parseFloat(ledVoltageInput.value), ledVoltageUnit.value, 'voltage');
        const i = convertToBase(parseFloat(ledCurrentInput.value), ledCurrentUnit.value, 'current');
        const numLeds = parseInt(numLedsInput.value);

        // Calculate total LED voltage
        const totalVled = vled * numLeds;

        // Check if supply voltage is sufficient
        if (vs <= totalVled) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Supply voltage must be greater than the total LED forward voltage.</p>';
            return;
        }

        // Calculate resistor value
        const r = (vs - totalVled) / i;

        // Calculate resistor power dissipation
        const p = i * i * r;

        // Display results
        let results = [];
        results.push(`Required Resistor Value: ${formatNumber(r)} Ω`);
        results.push(`Resistor Power Rating: ${formatNumber(p)} W`);
        results.push(`Voltage Across Resistor: ${formatNumber(vs - totalVled)} V`);
        results.push(`Current Through Circuit: ${formatNumber(i)} A`);

        let html = '<ul>';
        results.forEach(result => {
            html += `<li>${result}</li>`;
        });
        html += '</ul>';
        resultsContent.innerHTML = html;
    }

    // Function to reset calculator
    function reset() {
        supplyVoltageInput.value = '';
        ledVoltageInput.value = '';
        ledCurrentInput.value = '';
        numLedsInput.value = '1';
        supplyVoltageUnit.value = 'V';
        ledVoltageUnit.value = 'V';
        ledCurrentUnit.value = 'mA';
        resultsContent.innerHTML = '';
    }

    // Add input validation on keypress
    [supplyVoltageInput, ledVoltageInput, ledCurrentInput, numLedsInput].forEach(input => {
        input.addEventListener('keypress', (e) => {
            // Allow only numbers, decimal point, and control characters
            if (!/[\d.]/.test(e.key) && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
            }
        });
    });
})();
</script> 