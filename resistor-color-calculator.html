<!-- Resistor Color Code Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .resistor-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .resistor-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }

    /* Header styles */
    .resistor-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .resistor-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }

    .resistor-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }

    /* Mode selector */
    .resistor-calc .mode-selector {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    .resistor-calc .mode-toggle {
        display: flex;
        background-color: #f7fafc;
        border-radius: 10px;
        padding: 4px;
        border: 1px solid #e1e8ed;
    }

    .resistor-calc .mode-btn {
        padding: 12px 24px;
        border: none;
        background-color: transparent;
        color: #2c3e50;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .resistor-calc .mode-btn.active {
        background-color: #3498db;
        color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Band selector */
    .resistor-calc .band-selector {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 25px;
    }

    .resistor-calc .band-btn {
        padding: 12px 20px;
        border: 1px solid #3498db;
        background-color: #fff;
        color: #3498db;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .resistor-calc .band-btn.active {
        background-color: #3498db;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Calculator section */
    .resistor-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .resistor-calc .resistor-visual {
        text-align: center;
        margin-bottom: 25px;
        padding: 20px;
        background-color: #fff;
        border-radius: 8px;
        border: 1px solid #e1e8ed;
    }

    .resistor-calc .resistor-body {
        display: inline-block;
        width: 300px;
        height: 60px;
        background: linear-gradient(to bottom, #d4af37, #b8860b);
        border-radius: 30px;
        position: relative;
        margin: 20px 0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .resistor-calc .resistor-lead {
        position: absolute;
        width: 40px;
        height: 4px;
        background-color: #c0c0c0;
        top: 28px;
    }

    .resistor-calc .resistor-lead.left {
        left: -40px;
    }

    .resistor-calc .resistor-lead.right {
        right: -40px;
    }

    .resistor-calc .color-band {
        position: absolute;
        width: 20px;
        height: 60px;
        border-radius: 2px;
        top: 0;
        border: 1px solid rgba(0,0,0,0.2);
    }

    .resistor-calc .band1 { left: 60px; }
    .resistor-calc .band2 { left: 90px; }
    .resistor-calc .band3 { left: 120px; }
    .resistor-calc .band4 { left: 200px; }
    .resistor-calc .band5 { left: 150px; }
    .resistor-calc .band6 { left: 230px; }

    .resistor-calc .color-selectors {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .resistor-calc .color-group {
        display: flex;
        flex-direction: column;
    }

    .resistor-calc .color-group label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }

    .resistor-calc .color-options {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .resistor-calc .color-option {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.2s ease;
        position: relative;
    }

    .resistor-calc .color-option:hover {
        transform: scale(1.1);
        border-color: #2c3e50;
    }

    .resistor-calc .color-option.selected {
        border-color: #3498db;
        transform: scale(1.2);
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
    }

    /* Dropdown selector for colors */
    .resistor-calc .color-dropdown {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        cursor: pointer;
    }

    .resistor-calc .color-dropdown:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    /* Value input mode */
    .resistor-calc .value-input-section {
        display: none;
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e1e8ed;
        margin-bottom: 20px;
    }

    .resistor-calc .value-input-group {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 15px;
        align-items: end;
    }

    .resistor-calc .value-input-group input,
    .resistor-calc .value-input-group select {
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .resistor-calc .value-input-group input:focus,
    .resistor-calc .value-input-group select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .resistor-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .resistor-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }

    .resistor-calc .calculate {
        background-color: #3498db;
        color: white;
    }

    .resistor-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .resistor-calc .reset {
        background-color: #e74c3c;
        color: white;
    }

    .resistor-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Results section */
    .resistor-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }

    .resistor-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .resistor-calc .results ul {
        list-style: none;
    }

    .resistor-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .resistor-calc {
            padding: 15px;
        }

        .resistor-calc h1 {
            font-size: 1.7rem;
        }

        .resistor-calc .resistor-body {
            width: 280px;
            height: 50px;
        }

        .resistor-calc .color-band {
            width: 16px;
            height: 50px;
        }

        .resistor-calc .band1 { left: 55px; }
        .resistor-calc .band2 { left: 75px; }
        .resistor-calc .band3 { left: 95px; }
        .resistor-calc .band4 { left: 180px; }
        .resistor-calc .band5 { left: 115px; }
        .resistor-calc .band6 { left: 200px; }

        .resistor-calc .color-selectors {
            grid-template-columns: 1fr;
        }

        .resistor-calc .band-selector {
            flex-wrap: wrap;
            gap: 8px;
        }

        .resistor-calc .band-btn {
            padding: 10px 16px;
            font-size: 0.9rem;
        }

        .resistor-calc .mode-btn {
            padding: 10px 18px;
            font-size: 0.9rem;
        }

        .resistor-calc .value-input-group {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }

    @media (max-width: 480px) {
        .resistor-calc {
            padding: 12px;
        }

        .resistor-calc h1 {
            font-size: 1.5rem;
        }

        .resistor-calc .resistor-body {
            width: 240px;
            height: 45px;
        }

        .resistor-calc .color-band {
            width: 14px;
            height: 45px;
        }

        .resistor-calc .band1 { left: 45px; }
        .resistor-calc .band2 { left: 62px; }
        .resistor-calc .band3 { left: 79px; }
        .resistor-calc .band4 { left: 150px; }
        .resistor-calc .band5 { left: 96px; }
        .resistor-calc .band6 { left: 167px; }

        .resistor-calc .calculator {
            padding: 20px;
        }

        .resistor-calc .content-section {
            padding: 20px;
        }

        .resistor-calc .color-table {
            font-size: 0.85rem;
        }

        .resistor-calc .color-table th,
        .resistor-calc .color-table td {
            padding: 8px 6px;
        }
    }
</style>

<div class="resistor-calc">
    <header>
        <h1>Resistor Color Code Calculator</h1>
        <p class="intro">Decode resistor values from color bands with 100% accuracy. Calculate resistance, tolerance, and temperature coefficient for 4, 5, and 6 band resistors.</p>
    </header>

    <div class="mode-selector">
        <div class="mode-toggle">
            <button class="mode-btn active" onclick="switchMode('decode')">Color to Value</button>
            <button class="mode-btn" onclick="switchMode('encode')">Value to Color</button>
        </div>
    </div>

    <div class="band-selector" id="band-selector">
        <button class="band-btn active" onclick="selectBands(4)">4 Band</button>
        <button class="band-btn" onclick="selectBands(5)">5 Band</button>
        <button class="band-btn" onclick="selectBands(6)">6 Band</button>
    </div>

    <div class="calculator">
        <div class="resistor-visual">
            <h3 id="resistor-title">4-Band Resistor</h3>
            <div class="resistor-body">
                <div class="resistor-lead left"></div>
                <div class="resistor-lead right"></div>
                <div class="color-band band1" id="band1" style="background-color: #8B4513;"></div>
                <div class="color-band band2" id="band2" style="background-color: #000000;"></div>
                <div class="color-band band3" id="band3" style="background-color: #FF0000;"></div>
                <div class="color-band band4" id="band4" style="background-color: #FFD700;"></div>
                <div class="color-band band5" id="band5" style="background-color: #8B4513; display: none;"></div>
                <div class="color-band band6" id="band6" style="background-color: #FF0000; display: none;"></div>
            </div>
            <p id="resistor-description">Standard precision resistor with ±5% tolerance</p>
        </div>

        <!-- Value to Color Input Section -->
        <div class="value-input-section" id="value-input-section">
            <h3>Enter Resistance Value</h3>
            <div class="value-input-group">
                <div class="input-field">
                    <label for="resistance-value">Resistance Value</label>
                    <input type="number" id="resistance-value" placeholder="Enter resistance" step="0.01" min="0">
                </div>
                <div class="input-field">
                    <label for="resistance-unit">Unit</label>
                    <select id="resistance-unit">
                        <option value="ohm">Ω</option>
                        <option value="kohm">kΩ</option>
                        <option value="mohm">MΩ</option>
                    </select>
                </div>
                <div class="input-field">
                    <label for="tolerance-select">Tolerance</label>
                    <select id="tolerance-select">
                        <option value="10">±10% (Silver)</option>
                        <option value="5" selected>±5% (Gold)</option>
                        <option value="2">±2% (Red)</option>
                        <option value="1">±1% (Brown)</option>
                        <option value="0.5">±0.5% (Green)</option>
                        <option value="0.25">±0.25% (Blue)</option>
                        <option value="0.1">±0.1% (Violet)</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="color-selectors" id="color-selectors">
            <!-- Color selectors will be dynamically generated -->
        </div>

        <div class="button-group">
            <button class="btn calculate" onclick="calculateResistance()">Calculate</button>
            <button class="btn reset" onclick="resetCalculator()">Reset</button>
        </div>

        <div class="results" id="results" style="display: none;">
            <h3 id="results-title">Resistance Value</h3>
            <ul id="results-list">
                <!-- Results will be displayed here -->
            </ul>
        </div>
    </div>
</div>

<script>
    let currentBands = 4;
    let currentMode = 'decode'; // 'decode' for color to value, 'encode' for value to color
    let selectedColors = {
        band1: 'brown',
        band2: 'black',
        band3: 'red',
        band4: 'gold',
        band5: 'brown',
        band6: 'red'
    };

    const colorData = {
        black: { value: 0, multiplier: 1, tolerance: null, tempCoeff: null, color: '#000000', name: 'Black' },
        brown: { value: 1, multiplier: 10, tolerance: 1, tempCoeff: 100, color: '#8B4513', name: 'Brown' },
        red: { value: 2, multiplier: 100, tolerance: 2, tempCoeff: 50, color: '#FF0000', name: 'Red' },
        orange: { value: 3, multiplier: 1000, tolerance: null, tempCoeff: 15, color: '#FFA500', name: 'Orange' },
        yellow: { value: 4, multiplier: 10000, tolerance: null, tempCoeff: 25, color: '#FFFF00', name: 'Yellow' },
        green: { value: 5, multiplier: 100000, tolerance: 0.5, tempCoeff: null, color: '#008000', name: 'Green' },
        blue: { value: 6, multiplier: 1000000, tolerance: 0.25, tempCoeff: 10, color: '#0000FF', name: 'Blue' },
        violet: { value: 7, multiplier: 10000000, tolerance: 0.1, tempCoeff: 5, color: '#8A2BE2', name: 'Violet' },
        grey: { value: 8, multiplier: 100000000, tolerance: 0.05, tempCoeff: null, color: '#808080', name: 'Grey' },
        white: { value: 9, multiplier: 1000000000, tolerance: null, tempCoeff: 1, color: '#FFFFFF', name: 'White' },
        gold: { value: null, multiplier: 0.1, tolerance: 5, tempCoeff: null, color: '#FFD700', name: 'Gold' },
        silver: { value: null, multiplier: 0.01, tolerance: 10, tempCoeff: null, color: '#C0C0C0', name: 'Silver' }
    };

    function switchMode(mode) {
        currentMode = mode;

        // Update active button
        document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Show/hide appropriate sections
        const valueInputSection = document.getElementById('value-input-section');
        const colorSelectors = document.getElementById('color-selectors');
        const bandSelector = document.getElementById('band-selector');
        const resultsTitle = document.getElementById('results-title');

        if (mode === 'encode') {
            valueInputSection.style.display = 'block';
            colorSelectors.style.display = 'none';
            bandSelector.style.display = 'none';
            resultsTitle.textContent = 'Color Code Result';
        } else {
            valueInputSection.style.display = 'none';
            colorSelectors.style.display = 'grid';
            bandSelector.style.display = 'flex';
            resultsTitle.textContent = 'Resistance Value';
            generateColorSelectors();
        }

        document.getElementById('results').style.display = 'none';
    }

    function selectBands(bands) {
        currentBands = bands;

        // Update active button
        document.querySelectorAll('.band-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Update visual
        updateResistorVisual();
        generateColorSelectors();

        // Update title and description
        const titles = {
            4: '4-Band Resistor',
            5: '5-Band Resistor',
            6: '6-Band Resistor'
        };

        const descriptions = {
            4: 'Standard precision resistor with ±5% or ±10% tolerance',
            5: 'High precision resistor with ±1% or ±2% tolerance',
            6: 'High precision resistor with temperature coefficient'
        };

        document.getElementById('resistor-title').textContent = titles[bands];
        document.getElementById('resistor-description').textContent = descriptions[bands];
    }

    function updateResistorVisual() {
        // Hide all bands first
        for (let i = 1; i <= 6; i++) {
            document.getElementById(`band${i}`).style.display = 'none';
        }

        // Show appropriate bands
        if (currentBands === 4) {
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`band${i}`).style.display = 'block';
            }
        } else if (currentBands === 5) {
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`band${i}`).style.display = 'block';
            }
        } else if (currentBands === 6) {
            for (let i = 1; i <= 6; i++) {
                document.getElementById(`band${i}`).style.display = 'block';
            }
        }

        // Update band colors
        updateBandColors();
    }

    function updateBandColors() {
        for (let i = 1; i <= 6; i++) {
            const bandElement = document.getElementById(`band${i}`);
            const colorName = selectedColors[`band${i}`];
            if (colorData[colorName]) {
                bandElement.style.backgroundColor = colorData[colorName].color;
                if (colorName === 'white') {
                    bandElement.style.border = '2px solid #ccc';
                } else {
                    bandElement.style.border = '1px solid rgba(0,0,0,0.2)';
                }
            }
        }
    }

    function generateColorSelectors() {
        const container = document.getElementById('color-selectors');
        container.innerHTML = '';

        const bandLabels = {
            4: ['1st Digit', '2nd Digit', 'Multiplier', 'Tolerance'],
            5: ['1st Digit', '2nd Digit', '3rd Digit', 'Multiplier', 'Tolerance'],
            6: ['1st Digit', '2nd Digit', '3rd Digit', 'Multiplier', 'Tolerance', 'Temp Coeff']
        };

        const availableColors = {
            digit: ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey', 'white'],
            multiplier: ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey', 'white', 'gold', 'silver'],
            tolerance: ['brown', 'red', 'green', 'blue', 'violet', 'grey', 'gold', 'silver'],
            tempCoeff: ['brown', 'red', 'orange', 'yellow', 'blue', 'violet', 'white']
        };

        for (let i = 1; i <= currentBands; i++) {
            const colorGroup = document.createElement('div');
            colorGroup.className = 'color-group';

            const label = document.createElement('label');
            label.textContent = bandLabels[currentBands][i-1];
            colorGroup.appendChild(label);

            // Create dropdown instead of color options
            const dropdown = document.createElement('select');
            dropdown.className = 'color-dropdown';
            dropdown.id = `band${i}-dropdown`;

            let colorsToShow;
            if (i <= (currentBands === 4 ? 2 : 3)) {
                colorsToShow = availableColors.digit;
            } else if (i === (currentBands === 4 ? 3 : 4)) {
                colorsToShow = availableColors.multiplier;
            } else if (i === (currentBands === 4 ? 4 : 5)) {
                colorsToShow = availableColors.tolerance;
            } else if (i === 6) {
                colorsToShow = availableColors.tempCoeff;
            }

            colorsToShow.forEach(colorName => {
                const option = document.createElement('option');
                option.value = colorName;
                option.textContent = colorData[colorName].name;
                option.style.backgroundColor = colorData[colorName].color;
                option.style.color = (colorName === 'black' || colorName === 'blue') ? 'white' : 'black';

                if (selectedColors[`band${i}`] === colorName) {
                    option.selected = true;
                }

                dropdown.appendChild(option);
            });

            dropdown.onchange = (e) => selectColor(i, e.target.value);
            colorGroup.appendChild(dropdown);
            container.appendChild(colorGroup);
        }
    }

    function selectColor(bandNumber, colorName) {
        selectedColors[`band${bandNumber}`] = colorName;
        updateBandColors();
        generateColorSelectors();
    }

    function calculateResistance() {
        if (currentMode === 'encode') {
            calculateColorCode();
            return;
        }

        let resistance, tolerance, tempCoeff;

        if (currentBands === 4) {
            const digit1 = colorData[selectedColors.band1].value;
            const digit2 = colorData[selectedColors.band2].value;
            const multiplier = colorData[selectedColors.band3].multiplier;
            tolerance = colorData[selectedColors.band4].tolerance;

            resistance = (digit1 * 10 + digit2) * multiplier;

        } else if (currentBands === 5) {
            const digit1 = colorData[selectedColors.band1].value;
            const digit2 = colorData[selectedColors.band2].value;
            const digit3 = colorData[selectedColors.band3].value;
            const multiplier = colorData[selectedColors.band4].multiplier;
            tolerance = colorData[selectedColors.band5].tolerance;

            resistance = (digit1 * 100 + digit2 * 10 + digit3) * multiplier;

        } else if (currentBands === 6) {
            const digit1 = colorData[selectedColors.band1].value;
            const digit2 = colorData[selectedColors.band2].value;
            const digit3 = colorData[selectedColors.band3].value;
            const multiplier = colorData[selectedColors.band4].multiplier;
            tolerance = colorData[selectedColors.band5].tolerance;
            tempCoeff = colorData[selectedColors.band6].tempCoeff;

            resistance = (digit1 * 100 + digit2 * 10 + digit3) * multiplier;
        }

        // Format resistance value
        let resistanceStr;
        if (resistance >= 1000000) {
            resistanceStr = (resistance / 1000000).toFixed(2) + ' MΩ';
        } else if (resistance >= 1000) {
            resistanceStr = (resistance / 1000).toFixed(2) + ' kΩ';
        } else {
            resistanceStr = resistance.toFixed(2) + ' Ω';
        }

        // Calculate tolerance range
        const toleranceValue = (resistance * tolerance) / 100;
        const minResistance = resistance - toleranceValue;
        const maxResistance = resistance + toleranceValue;

        let minStr, maxStr;
        if (minResistance >= 1000000) {
            minStr = (minResistance / 1000000).toFixed(2) + ' MΩ';
            maxStr = (maxResistance / 1000000).toFixed(2) + ' MΩ';
        } else if (minResistance >= 1000) {
            minStr = (minResistance / 1000).toFixed(2) + ' kΩ';
            maxStr = (maxResistance / 1000).toFixed(2) + ' kΩ';
        } else {
            minStr = minResistance.toFixed(2) + ' Ω';
            maxStr = maxResistance.toFixed(2) + ' Ω';
        }

        // Display results
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = '';

        const results = [
            `Resistance: ${resistanceStr}`,
            `Tolerance: ±${tolerance}%`,
            `Range: ${minStr} to ${maxStr}`
        ];

        if (currentBands === 6 && tempCoeff) {
            results.push(`Temperature Coefficient: ${tempCoeff} ppm/°C`);
        }

        results.forEach(result => {
            const li = document.createElement('li');
            li.textContent = result;
            resultsList.appendChild(li);
        });

        document.getElementById('results').style.display = 'block';
    }

    function calculateColorCode() {
        const resistanceValue = parseFloat(document.getElementById('resistance-value').value);
        const unit = document.getElementById('resistance-unit').value;
        const tolerance = parseFloat(document.getElementById('tolerance-select').value);

        if (!resistanceValue || resistanceValue <= 0) {
            alert('Please enter a valid resistance value');
            return;
        }

        // Convert to ohms
        let valueInOhms = resistanceValue;
        if (unit === 'kohm') {
            valueInOhms = resistanceValue * 1000;
        } else if (unit === 'mohm') {
            valueInOhms = resistanceValue * 1000000;
        }

        // Find the best representation
        let significantDigits, multiplier;
        let tempValue = valueInOhms;
        let multiplierPower = 0;

        // Normalize to get significant digits
        while (tempValue >= 1000 && multiplierPower < 9) {
            tempValue /= 10;
            multiplierPower++;
        }

        while (tempValue < 10 && multiplierPower > -2) {
            tempValue *= 10;
            multiplierPower--;
        }

        if (currentBands === 4) {
            // 4-band: 2 significant digits
            significantDigits = Math.round(tempValue);
            if (significantDigits >= 100) {
                significantDigits = Math.round(significantDigits / 10);
                multiplierPower++;
            }

            const digit1 = Math.floor(significantDigits / 10);
            const digit2 = significantDigits % 10;

            selectedColors.band1 = getColorByValue(digit1);
            selectedColors.band2 = getColorByValue(digit2);
            selectedColors.band3 = getColorByMultiplier(Math.pow(10, multiplierPower));
            selectedColors.band4 = getColorByTolerance(tolerance);

        } else {
            // 5-band: 3 significant digits
            significantDigits = Math.round(tempValue * 10);
            if (significantDigits >= 1000) {
                significantDigits = Math.round(significantDigits / 10);
                multiplierPower++;
            }

            const digit1 = Math.floor(significantDigits / 100);
            const digit2 = Math.floor((significantDigits % 100) / 10);
            const digit3 = significantDigits % 10;

            selectedColors.band1 = getColorByValue(digit1);
            selectedColors.band2 = getColorByValue(digit2);
            selectedColors.band3 = getColorByValue(digit3);
            selectedColors.band4 = getColorByMultiplier(Math.pow(10, multiplierPower));
            selectedColors.band5 = getColorByTolerance(tolerance);
        }

        updateBandColors();

        // Display results
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = '';

        const results = [
            `Original Value: ${resistanceValue} ${unit === 'ohm' ? 'Ω' : unit === 'kohm' ? 'kΩ' : 'MΩ'}`,
            `Encoded Value: ${formatResistance(valueInOhms)} ±${tolerance}%`,
            `Band 1 (${currentBands === 4 ? '1st Digit' : '1st Digit'}): ${colorData[selectedColors.band1].name}`,
            `Band 2 (${currentBands === 4 ? '2nd Digit' : '2nd Digit'}): ${colorData[selectedColors.band2].name}`,
            currentBands === 4 ?
                `Band 3 (Multiplier): ${colorData[selectedColors.band3].name}` :
                `Band 3 (3rd Digit): ${colorData[selectedColors.band3].name}`,
            currentBands === 4 ?
                `Band 4 (Tolerance): ${colorData[selectedColors.band4].name}` :
                `Band 4 (Multiplier): ${colorData[selectedColors.band4].name}`,
            currentBands === 5 ? `Band 5 (Tolerance): ${colorData[selectedColors.band5].name}` : null
        ].filter(Boolean);

        results.forEach(result => {
            const li = document.createElement('li');
            li.textContent = result;
            resultsList.appendChild(li);
        });

        document.getElementById('results').style.display = 'block';
    }

    function getColorByValue(digit) {
        const colorMap = ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'grey', 'white'];
        return colorMap[digit] || 'black';
    }

    function getColorByMultiplier(multiplier) {
        const multiplierMap = {
            1: 'black',
            10: 'brown',
            100: 'red',
            1000: 'orange',
            10000: 'yellow',
            100000: 'green',
            1000000: 'blue',
            10000000: 'violet',
            100000000: 'grey',
            1000000000: 'white',
            0.1: 'gold',
            0.01: 'silver'
        };
        return multiplierMap[multiplier] || 'black';
    }

    function getColorByTolerance(tolerance) {
        const toleranceMap = {
            10: 'silver',
            5: 'gold',
            2: 'red',
            1: 'brown',
            0.5: 'green',
            0.25: 'blue',
            0.1: 'violet'
        };
        return toleranceMap[tolerance] || 'gold';
    }

    function resetCalculator() {
        selectedColors = {
            band1: 'brown',
            band2: 'black',
            band3: 'red',
            band4: 'gold',
            band5: 'brown',
            band6: 'red'
        };

        // Reset value input fields
        document.getElementById('resistance-value').value = '';
        document.getElementById('resistance-unit').value = 'ohm';
        document.getElementById('tolerance-select').value = '5';

        updateBandColors();
        if (currentMode === 'decode') {
            generateColorSelectors();
        }
        document.getElementById('results').style.display = 'none';
    }

    // Initialize calculator
    document.addEventListener('DOMContentLoaded', function() {
        selectBands(4);
        switchMode('decode'); // Start in decode mode
    });
</script>

<!-- Content section -->
<div class="resistor-calc">
    <div class="content-section">
        <h2>Understanding Resistor Color Codes</h2>
        <p>Resistor color codes are a standardized system used to identify the resistance value, tolerance, and sometimes temperature coefficient of resistors. This system was developed to provide a compact way to mark resistor values on small components where numerical printing would be difficult to read.</p>

        <h3>How Resistor Color Codes Work</h3>
        <p>The color code system uses colored bands painted around the resistor body to represent different digits and multipliers. The position and number of bands determine how to read the resistor value:</p>

        <div class="highlight-box">
            <h3>4-Band Resistors (Standard)</h3>
            <p><strong>Band 1:</strong> First significant digit</p>
            <p><strong>Band 2:</strong> Second significant digit</p>
            <p><strong>Band 3:</strong> Multiplier (number of zeros)</p>
            <p><strong>Band 4:</strong> Tolerance (accuracy)</p>
        </div>

        <div class="highlight-box">
            <h3>5-Band Resistors (Precision)</h3>
            <p><strong>Band 1:</strong> First significant digit</p>
            <p><strong>Band 2:</strong> Second significant digit</p>
            <p><strong>Band 3:</strong> Third significant digit</p>
            <p><strong>Band 4:</strong> Multiplier (number of zeros)</p>
            <p><strong>Band 5:</strong> Tolerance (accuracy)</p>
        </div>

        <div class="highlight-box">
            <h3>6-Band Resistors (High Precision)</h3>
            <p><strong>Band 1-3:</strong> Same as 5-band resistor</p>
            <p><strong>Band 4:</strong> Multiplier</p>
            <p><strong>Band 5:</strong> Tolerance</p>
            <p><strong>Band 6:</strong> Temperature coefficient (ppm/°C)</p>
        </div>

        <h3>Color Code Reference Table</h3>
        <table class="color-table">
            <thead>
                <tr>
                    <th>Color</th>
                    <th>Digit</th>
                    <th>Multiplier</th>
                    <th>Tolerance</th>
                    <th>Temp Coeff</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="color-sample" style="background-color: #000000;"></span>Black</td>
                    <td>0</td>
                    <td>×1</td>
                    <td>-</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #8B4513;"></span>Brown</td>
                    <td>1</td>
                    <td>×10</td>
                    <td>±1%</td>
                    <td>100 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #FF0000;"></span>Red</td>
                    <td>2</td>
                    <td>×100</td>
                    <td>±2%</td>
                    <td>50 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #FFA500;"></span>Orange</td>
                    <td>3</td>
                    <td>×1K</td>
                    <td>-</td>
                    <td>15 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #FFFF00; border: 1px solid #ccc;"></span>Yellow</td>
                    <td>4</td>
                    <td>×10K</td>
                    <td>-</td>
                    <td>25 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #008000;"></span>Green</td>
                    <td>5</td>
                    <td>×100K</td>
                    <td>±0.5%</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #0000FF;"></span>Blue</td>
                    <td>6</td>
                    <td>×1M</td>
                    <td>±0.25%</td>
                    <td>10 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #8A2BE2;"></span>Violet</td>
                    <td>7</td>
                    <td>×10M</td>
                    <td>±0.1%</td>
                    <td>5 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #808080;"></span>Grey</td>
                    <td>8</td>
                    <td>×100M</td>
                    <td>±0.05%</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #FFFFFF; border: 1px solid #ccc;"></span>White</td>
                    <td>9</td>
                    <td>×1G</td>
                    <td>-</td>
                    <td>1 ppm/°C</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #FFD700;"></span>Gold</td>
                    <td>-</td>
                    <td>×0.1</td>
                    <td>±5%</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td><span class="color-sample" style="background-color: #C0C0C0;"></span>Silver</td>
                    <td>-</td>
                    <td>×0.01</td>
                    <td>±10%</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>

        <h3>Reading Direction and Orientation</h3>
        <p>Resistors should be read from left to right, starting with the band closest to one end. The tolerance band (usually gold or silver) is typically positioned closer to one end, helping you identify the correct reading direction. If you're unsure, try both directions and see which gives a standard resistor value.</p>

        <h3>Common Resistor Values and Applications</h3>
        <p>Resistors are manufactured in standard values following the E-series (E12, E24, E96, E192). Common applications include:</p>
        <ul>
            <li><strong>Pull-up/Pull-down resistors:</strong> 1kΩ - 10kΩ</li>
            <li><strong>Current limiting:</strong> 220Ω - 1kΩ for LEDs</li>
            <li><strong>Voltage dividers:</strong> Various values depending on ratio needed</li>
            <li><strong>Timing circuits:</strong> 1kΩ - 1MΩ with capacitors</li>
            <li><strong>Biasing circuits:</strong> 10kΩ - 100kΩ in amplifiers</li>
        </ul>

        <h3>Tolerance and Precision</h3>
        <p>Tolerance indicates how much the actual resistance can vary from the nominal value. Lower tolerance means higher precision:</p>
        <ul>
            <li><strong>±20% (No band):</strong> Very old resistors</li>
            <li><strong>±10% (Silver):</strong> Standard tolerance</li>
            <li><strong>±5% (Gold):</strong> Common precision</li>
            <li><strong>±1% (Brown):</strong> High precision</li>
            <li><strong>±0.5% (Green):</strong> Very high precision</li>
            <li><strong>±0.25% (Blue):</strong> Ultra precision</li>
            <li><strong>±0.1% (Violet):</strong> Laboratory grade</li>
        </ul>

        <h3>Temperature Coefficient</h3>
        <p>The temperature coefficient (6th band) indicates how much the resistance changes with temperature, measured in parts per million per degree Celsius (ppm/°C). Lower values indicate better temperature stability, crucial for precision applications.</p>

        <h3>Tips for Accurate Reading</h3>
        <ul>
            <li>Use good lighting and magnification if needed</li>
            <li>Clean the resistor surface if dirty or oxidized</li>
            <li>Be aware that some colors can appear similar (red vs. orange, brown vs. red)</li>
            <li>Use a multimeter to verify your reading</li>
            <li>Consider the context - check if the calculated value makes sense in the circuit</li>
        </ul>

        <h3>Modern Alternatives</h3>
        <p>While color codes remain standard for through-hole resistors, surface-mount resistors use numerical codes. Some modern resistors also include QR codes or direct numerical printing for easier identification.</p>
    </div>
</div>
