<!-- Ohm's Law Calculator for WordPress -->
<style>
    /* Reset and base styles */
    .ohms-law-calc * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
    
    .ohms-law-calc {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
        padding: 25px;
        max-width: 900px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }
    
    /* Header styles */
    .ohms-law-calc header {
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }
    
    .ohms-law-calc h1 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .ohms-law-calc .intro {
        font-size: 1rem;
        color: #666;
        margin-bottom: 5px;
    }
    
    /* Calculator section */
    .ohms-law-calc .calculator {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ohms-law-calc .input-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .ohms-law-calc .input-field {
        display: flex;
        flex-direction: column;
    }
    
    .ohms-law-calc .input-field label {
        margin-bottom: 10px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 1.05rem;
    }
    
    .ohms-law-calc .input-with-unit {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .ohms-law-calc .input-with-unit input {
        flex: 3;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
    }
    
    .ohms-law-calc .input-with-unit input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .ohms-law-calc .input-with-unit select {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1.1rem;
        background-color: #fff;
        min-width: 60px;
        max-width: 80px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        height: 48px;
        cursor: pointer;
    }
    
    .ohms-law-calc .input-with-unit select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }
    
    .ohms-law-calc .button-group {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .ohms-law-calc .btn {
        padding: 14px 25px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 52px;
    }
    
    .ohms-law-calc .calculate {
        background-color: #3498db;
        color: white;
    }
    
    .ohms-law-calc .calculate:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .ohms-law-calc .reset {
        background-color: #e74c3c;
        color: white;
    }
    
    .ohms-law-calc .reset:hover {
        background-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Results section */
    .ohms-law-calc .results {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e1e8ed;
    }
    
    .ohms-law-calc .results h3 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }
    
    .ohms-law-calc .results ul {
        list-style: none;
    }
    
    .ohms-law-calc .results li {
        margin-bottom: 12px;
        color: #2c3e50;
        padding: 12px 16px;
        background-color: #f0f6fa;
        border-radius: 8px;
        font-weight: 500;
        font-size: 1.1rem;
    }
    
    /* Formula section */
    .ohms-law-calc .formulas {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ohms-law-calc .formulas h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
    }
    
    .ohms-law-calc .formula-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .ohms-law-calc .formula-item {
        text-align: center;
        padding: 20px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .ohms-law-calc .formula-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }
    
    .ohms-law-calc .formula-item h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }
    
    .ohms-law-calc .formula-item p {
        font-size: 1rem;
        color: #555;
    }
    
    /* Content section */
    .ohms-law-calc .content-section {
        background-color: #f7fafc;
        padding: 30px;
        border-radius: 10px;
        margin-top: 25px;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .ohms-law-calc .content-section h2 {
        color: #2c3e50;
        margin-bottom: 18px;
        font-size: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }
    
    .ohms-law-calc .content-section h3 {
        color: #2c3e50;
        margin: 20px 0 10px;
        font-size: 1.25rem;
    }
    
    .ohms-law-calc .content-section p {
        margin-bottom: 15px;
        color: #555;
        line-height: 1.7;
    }
    
    .ohms-law-calc .content-section strong {
        color: #2c3e50;
        font-weight: 600;
    }
    
    .ohms-law-calc .content-section ol,
    .ohms-law-calc .content-section ul {
        margin-bottom: 20px;
        padding-left: 25px;
    }
    
    .ohms-law-calc .content-section li {
        margin-bottom: 8px;
        color: #555;
        line-height: 1.7;
    }
    
    /* Ad placement spacers */
    .ohms-law-calc .ad-spacer {
        height: 15px;
        width: 100%;
        margin: 25px 0;
        text-align: center;
        clear: both;
    }
    
    /* Highlight box for important content */
    .ohms-law-calc .highlight-box {
        background-color: #edf7ff;
        border-left: 4px solid #3498db;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
    }
    
    .ohms-law-calc .highlight-box h3 {
        margin-top: 0;
        color: #2c3e50;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .ohms-law-calc {
            padding: 15px;
            margin: 0 auto;
            border-radius: 8px;
        }
    
        .ohms-law-calc h1 {
            font-size: 1.5rem;
        }
        
        .ohms-law-calc .calculator,
        .ohms-law-calc .formulas,
        .ohms-law-calc .content-section {
            padding: 15px;
        }
    
        .ohms-law-calc .input-group {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .ohms-law-calc .formula-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    
        .ohms-law-calc .input-field label {
            font-size: 1rem;
            margin-bottom: 8px;
        }
        
        .ohms-law-calc .input-with-unit input {
            flex: 2;
        }
        
        .ohms-law-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
        }
        
        .ohms-law-calc .btn {
            font-size: 1rem;
            padding: 12px;
            height: 44px;
        }
        
        .ohms-law-calc .results li {
            font-size: 1rem;
            padding: 10px;
        }
        
        .ohms-law-calc .formula-item h3 {
            font-size: 1.1rem;
        }
        
        .ohms-law-calc .formula-item p {
            font-size: 0.9rem;
        }
        
        .ohms-law-calc .content-section {
            padding: 20px;
        }
        
        .ohms-law-calc .content-section h2 {
            font-size: 1.3rem;
        }
        
        .ohms-law-calc .content-section h3 {
            font-size: 1.15rem;
        }
    }
    
    @media (max-width: 480px) {
        .ohms-law-calc {
            padding: 12px;
        }
        
        .ohms-law-calc .input-with-unit {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
        
        .ohms-law-calc .input-with-unit input {
            min-height: 44px;
            flex: 3;
        }
        
        .ohms-law-calc .input-with-unit select {
            min-width: 60px;
            max-width: 70px;
            min-height: 44px;
        }
        
        .ohms-law-calc .formula-item {
            padding: 12px;
        }
        
        .ohms-law-calc .results li {
            padding: 10px;
        }
    }
    
    @media (max-width: 350px) {
        .ohms-law-calc .input-with-unit {
            flex-direction: column;
            gap: 6px;
        }
        
        .ohms-law-calc .input-with-unit select {
            width: 100%;
        }
        
        .ohms-law-calc .button-group {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

<div class="ohms-law-calc">
    <header>
        <h1>Ohm's Law Calculator</h1>
        <p class="intro">Calculate voltage, current, resistance, and power in electrical circuits using Ohm's Law. Enter any two known values to find the others instantly.</p>
    </header>

    <div class="calculator">
        <div class="input-group">
            <div class="input-field">
                <label for="voltage">Voltage (V)</label>
                <div class="input-with-unit">
                    <input type="number" id="voltage" placeholder="Enter voltage">
                    <select id="voltage-unit">
                        <option value="V">V</option>
                        <option value="mV">mV</option>
                        <option value="kV">kV</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="current">Current (I)</label>
                <div class="input-with-unit">
                    <input type="number" id="current" placeholder="Enter current">
                    <select id="current-unit">
                        <option value="A">A</option>
                        <option value="mA">mA</option>
                        <option value="µA">µA</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="resistance">Resistance (R)</label>
                <div class="input-with-unit">
                    <input type="number" id="resistance" placeholder="Enter resistance">
                    <select id="resistance-unit">
                        <option value="Ω">Ω</option>
                        <option value="kΩ">kΩ</option>
                        <option value="MΩ">MΩ</option>
                    </select>
                </div>
            </div>
            <div class="input-field">
                <label for="power">Power (P)</label>
                <div class="input-with-unit">
                    <input type="number" id="power" placeholder="Enter power">
                    <select id="power-unit">
                        <option value="W">W</option>
                        <option value="mW">mW</option>
                        <option value="kW">kW</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="button-group">
            <button id="calculate" class="btn calculate">Calculate</button>
            <button id="reset" class="btn reset">Reset</button>
        </div>
        <div class="results" id="results">
            <h3>Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <div class="formulas">
        <h2>Ohm's Law Formulas</h2>
        <div class="formula-grid">
            <div class="formula-item">
                <h3>V = I × R</h3>
                <p>Voltage = Current × Resistance</p>
            </div>
            <div class="formula-item">
                <h3>I = V / R</h3>
                <p>Current = Voltage / Resistance</p>
            </div>
            <div class="formula-item">
                <h3>R = V / I</h3>
                <p>Resistance = Voltage / Current</p>
            </div>
            <div class="formula-item">
                <h3>P = V × I</h3>
                <p>Power = Voltage × Current</p>
            </div>
        </div>
    </div>

    <div class="content-section">
        <h2>Understanding Ohm's Law</h2>
        <p>Ohm's Law is a fundamental principle in electrical engineering that describes the relationship between voltage, current, and resistance in an electrical circuit. It states that the current through a conductor is directly proportional to the voltage across it and inversely proportional to its resistance.</p>
        
        <div class="highlight-box">
            <h3>Key Concept</h3>
            <p>The mathematical equation for Ohm's Law is V = I × R, where V is voltage (in volts), I is current (in amperes), and R is resistance (in ohms).</p>
        </div>
        
        <h2>How to Use the Calculator</h2>
        <ol>
            <li>Enter any two known values from voltage (V), current (I), resistance (R), or power (P)</li>
            <li>Select the appropriate units for your input values</li>
            <li>Click "Calculate" to find the remaining values</li>
            <li>Use "Reset" to clear all inputs and start over</li>
        </ol>

        <h2>Common Applications</h2>
        <ul>
            <li>Circuit design and analysis</li>
            <li>Electronic component selection</li>
            <li>Power supply calculations</li>
            <li>Electrical troubleshooting</li>
            <li>Educational purposes</li>
        </ul>

        <h2>Important Notes</h2>
        <ul>
            <li>This calculator is designed for DC (direct current) circuits</li>
            <li>All calculations assume ideal conditions with no reactance</li>
            <li>For AC circuits, additional factors like impedance and power factor must be considered</li>
            <li>Always verify calculations with proper testing equipment</li>
        </ul>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <!-- Ad-friendly content sections -->
    <div class="content-section">
        <h2>The History of Ohm's Law</h2>
        <p>Ohm's Law was first published in 1827 by German physicist Georg Ohm. His experimental work established the direct proportionality between voltage and current in a conductor. Despite initial skepticism from the scientific community, Ohm's findings eventually gained wide acceptance and became one of the most fundamental laws in electrical engineering.</p>
        <p>In his honor, the unit of electrical resistance was named the "ohm" (symbol: Ω). Today, Ohm's Law serves as the cornerstone for analyzing and designing electrical circuits in everything from small electronic devices to large power distribution systems.</p>
        
        <div class="highlight-box">
            <h3>Historical Fact</h3>
            <p>Georg Ohm's discoveries were largely ignored for several years, and he faced criticism from other scientists. It was only later that his work gained recognition, eventually earning him the prestigious Copley Medal from the Royal Society in 1841.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Practical Examples of Ohm's Law Calculations</h2>
        
        <h3>Example 1: Finding Current in a Simple Circuit</h3>
        <p>A 12V battery is connected to a 6Ω resistor. Using Ohm's Law (I = V/R), we can calculate the current: I = 12V / 6Ω = 2A. This means 2 amperes of current will flow through the circuit.</p>
        
        <h3>Example 2: Calculating Power Consumption</h3>
        <p>If a circuit has a voltage of 120V and draws 5A of current, the power consumption can be calculated as P = V × I = 120V × 5A = 600W. This means the circuit consumes 600 watts of power.</p>
        
        <h3>Example 3: Determining Required Resistance</h3>
        <p>To limit the current to 0.5A in a circuit with a 9V battery, the required resistance would be R = V/I = 9V / 0.5A = 18Ω. This calculation helps in selecting the appropriate resistor for current-limiting applications.</p>
        
        <div class="highlight-box">
            <h3>Practical Tip</h3>
            <p>When designing circuits, always choose resistors with power ratings higher than your calculated power value to provide a safety margin. For example, if your calculations show a resistor will dissipate 0.25W, consider using at least a 0.5W resistor.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Understanding Units in Electrical Measurements</h2>
        
        <h3>Voltage Units</h3>
        <p><strong>Volt (V)</strong> - The standard unit of electrical potential or electromotive force. Common household outlets provide 120V or 230V depending on your location.</p>
        <p><strong>Millivolt (mV)</strong> - One thousandth of a volt (0.001V). Often used in measuring sensor outputs, thermocouple readings, and other low-voltage applications.</p>
        <p><strong>Kilovolt (kV)</strong> - One thousand volts (1000V). Used in power transmission, X-ray machines, and high-voltage applications.</p>
        
        <h3>Current Units</h3>
        <p><strong>Ampere (A)</strong> - The base unit of electrical current. Household appliances typically draw from less than 1A to about 15A.</p>
        <p><strong>Milliampere (mA)</strong> - One thousandth of an ampere (0.001A). Common in electronic circuits, LEDs, and small devices.</p>
        <p><strong>Microampere (µA)</strong> - One millionth of an ampere (0.000001A). Used in sensitive measurements and low-power devices.</p>
        
        <h3>Resistance Units</h3>
        <p><strong>Ohm (Ω)</strong> - The standard unit of electrical resistance. Common resistors range from a few ohms to several thousand ohms.</p>
        <p><strong>Kilohm (kΩ)</strong> - One thousand ohms (1000Ω). Frequently used in electronic circuits.</p>
        <p><strong>Megohm (MΩ)</strong> - One million ohms (1,000,000Ω). Used in high-resistance applications and insulation measurements.</p>
        
        <div class="highlight-box">
            <h3>Unit Conversion</h3>
            <p>When working with different units, remember these conversions:<br>
            1 kV = 1000 V = 1,000,000 mV<br>
            1 A = 1000 mA = 1,000,000 µA<br>
            1 MΩ = 1000 kΩ = 1,000,000 Ω</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Frequently Asked Questions</h2>
        
        <h3>What is the relationship between power and Ohm's Law?</h3>
        <p>While the basic Ohm's Law relates voltage, current, and resistance (V = I × R), power can be calculated by combining this with the power formula P = V × I. This gives us additional power equations: P = I²R and P = V²/R, which are useful when only certain variables are known.</p>
        
        <h3>Can Ohm's Law be applied to all electrical components?</h3>
        <p>Ohm's Law applies directly to ohmic (or linear) components like resistors, where resistance remains constant regardless of voltage and current. Non-linear components like diodes, transistors, and thermistors don't follow Ohm's Law in a straightforward manner as their resistance changes with current, voltage, or temperature.</p>
        
        <h3>How accurate is this calculator for real-world applications?</h3>
        <p>This calculator provides results based on ideal conditions, assuming perfect conductors and components. In real-world applications, factors like temperature, component tolerances, and connection quality can affect actual measurements. For critical applications, it's recommended to verify calculations with proper measurement instruments.</p>
        
        <h3>Why do I need to enter exactly two values?</h3>
        <p>Ohm's Law establishes a relationship between three electrical quantities: voltage, current, and resistance. If you know any two of these values, you can calculate the third. For power calculations, only one additional value is needed since power is defined by the relationship between voltage and current (P = V × I).</p>
        
        <div class="highlight-box">
            <h3>Common Mistake</h3>
            <p>One frequent error when applying Ohm's Law is forgetting to convert units. Always ensure all values use consistent units before calculating (e.g., convert milliamps to amps or kilohms to ohms). This calculator handles unit conversions automatically.</p>
        </div>
    </div>

    <!-- Ad spacer for potential ad placement -->
    <div class="ad-spacer"></div>

    <div class="content-section">
        <h2>Tips for Electrical Safety</h2>
        <p>While Ohm's Law calculations help in understanding electrical circuits, safety should always be your top priority when working with electricity:</p>
        
        <ul>
            <li><strong>Always disconnect power</strong> before working on electrical circuits</li>
            <li><strong>Use insulated tools</strong> designed for electrical work</li>
            <li><strong>Wear appropriate safety gear</strong> including insulating gloves when necessary</li>
            <li><strong>Never work on electrical systems</strong> in wet conditions</li>
            <li><strong>Verify circuits are de-energized</strong> using a reliable voltage tester</li>
            <li><strong>Follow local electrical codes</strong> and regulations</li>
            <li><strong>Consult a licensed electrician</strong> for complex installations or when unsure</li>
        </ul>
        
        <p>Remember, electrical calculations can help design safe systems, but proper safety procedures are essential when implementing them.</p>
        
        <div class="highlight-box">
            <h3>Safety Warning</h3>
            <p>Even low voltages can be dangerous under certain conditions. Never underestimate the potential hazards of electricity. When in doubt, seek professional assistance from a qualified electrician.</p>
        </div>
    </div>
</div>

<script>
(function() {
    // Get DOM elements
    const voltageInput = document.getElementById('voltage');
    const currentInput = document.getElementById('current');
    const resistanceInput = document.getElementById('resistance');
    const powerInput = document.getElementById('power');
    const voltageUnit = document.getElementById('voltage-unit');
    const currentUnit = document.getElementById('current-unit');
    const resistanceUnit = document.getElementById('resistance-unit');
    const powerUnit = document.getElementById('power-unit');
    const calculateBtn = document.getElementById('calculate');
    const resetBtn = document.getElementById('reset');
    const resultsContent = document.getElementById('results-content');

    // Add event listeners
    calculateBtn.addEventListener('click', calculate);
    resetBtn.addEventListener('click', reset);

    // Add keyboard support - enter key triggers calculation
    document.querySelectorAll('.ohms-law-calc input').forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculate();
                e.preventDefault();
            }
        });
    });

    // Unit conversion factors
    const unitFactors = {
        voltage: {
            'V': 1,
            'mV': 0.001,
            'kV': 1000
        },
        current: {
            'A': 1,
            'mA': 0.001,
            'µA': 0.000001
        },
        resistance: {
            'Ω': 1,
            'kΩ': 1000,
            'MΩ': 1000000
        },
        power: {
            'W': 1,
            'mW': 0.001,
            'kW': 1000
        }
    };

    // Function to convert value to base unit
    function convertToBase(value, unit, type) {
        return value * unitFactors[type][unit];
    }

    // Function to convert from base unit to target unit
    function convertFromBase(value, unit, type) {
        return value / unitFactors[type][unit];
    }

    // Function to count non-empty inputs
    function countNonEmptyInputs() {
        let count = 0;
        const inputs = [voltageInput, currentInput, resistanceInput, powerInput];
        inputs.forEach(input => {
            if (input.value !== '') count++;
        });
        return count;
    }

    // Function to validate inputs
    function validateInputs() {
        const inputs = [voltageInput, currentInput, resistanceInput, powerInput];
        for (let input of inputs) {
            if (input.value !== '' && (isNaN(input.value) || parseFloat(input.value) <= 0)) {
                return false;
            }
        }
        return true;
    }

    // Format number with appropriate decimal places
    function formatNumber(num) {
        // Handle very small numbers
        if (num < 0.01 && num > 0) {
            return num.toExponential(2);
        }
        // Handle very large numbers
        if (num > 1000000) {
            return num.toExponential(2);
        }
        // Regular numbers
        return num.toFixed(2);
    }

    // Function to calculate missing values
    function calculate() {
        // Clear previous results
        resultsContent.innerHTML = '';
        
        // Check if exactly two values are provided
        if (countNonEmptyInputs() !== 2) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter exactly two values to calculate the others.</p>';
            return;
        }

        // Validate inputs
        if (!validateInputs()) {
            resultsContent.innerHTML = '<p style="color: #e74c3c;">Please enter valid positive numbers.</p>';
            return;
        }

        // Get input values and convert to base units
        const v = voltageInput.value ? convertToBase(parseFloat(voltageInput.value), voltageUnit.value, 'voltage') : null;
        const i = currentInput.value ? convertToBase(parseFloat(currentInput.value), currentUnit.value, 'current') : null;
        const r = resistanceInput.value ? convertToBase(parseFloat(resistanceInput.value), resistanceUnit.value, 'resistance') : null;
        const p = powerInput.value ? convertToBase(parseFloat(powerInput.value), powerUnit.value, 'power') : null;

        // Calculate missing values in base units
        let results = [];

        // Case 1: Voltage and Current are known
        if (v !== null && i !== null) {
            const calculatedR = v / i;
            const calculatedP = v * i;
            results.push(`Resistance (R) = ${formatNumber(convertFromBase(calculatedR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
            results.push(`Power (P) = ${formatNumber(convertFromBase(calculatedP, powerUnit.value, 'power'))} ${powerUnit.value}`);
        }
        // Case 2: Voltage and Resistance are known
        else if (v !== null && r !== null) {
            const calculatedI = v / r;
            const calculatedP = (v * v) / r;
            results.push(`Current (I) = ${formatNumber(convertFromBase(calculatedI, currentUnit.value, 'current'))} ${currentUnit.value}`);
            results.push(`Power (P) = ${formatNumber(convertFromBase(calculatedP, powerUnit.value, 'power'))} ${powerUnit.value}`);
        }
        // Case 3: Voltage and Power are known
        else if (v !== null && p !== null) {
            const calculatedI = p / v;
            const calculatedR = (v * v) / p;
            results.push(`Current (I) = ${formatNumber(convertFromBase(calculatedI, currentUnit.value, 'current'))} ${currentUnit.value}`);
            results.push(`Resistance (R) = ${formatNumber(convertFromBase(calculatedR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
        }
        // Case 4: Current and Resistance are known
        else if (i !== null && r !== null) {
            const calculatedV = i * r;
            const calculatedP = i * i * r;
            results.push(`Voltage (V) = ${formatNumber(convertFromBase(calculatedV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
            results.push(`Power (P) = ${formatNumber(convertFromBase(calculatedP, powerUnit.value, 'power'))} ${powerUnit.value}`);
        }
        // Case 5: Current and Power are known
        else if (i !== null && p !== null) {
            const calculatedV = p / i;
            const calculatedR = p / (i * i);
            results.push(`Voltage (V) = ${formatNumber(convertFromBase(calculatedV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
            results.push(`Resistance (R) = ${formatNumber(convertFromBase(calculatedR, resistanceUnit.value, 'resistance'))} ${resistanceUnit.value}`);
        }
        // Case 6: Resistance and Power are known
        else if (r !== null && p !== null) {
            const calculatedI = Math.sqrt(p / r);
            const calculatedV = Math.sqrt(p * r);
            results.push(`Current (I) = ${formatNumber(convertFromBase(calculatedI, currentUnit.value, 'current'))} ${currentUnit.value}`);
            results.push(`Voltage (V) = ${formatNumber(convertFromBase(calculatedV, voltageUnit.value, 'voltage'))} ${voltageUnit.value}`);
        }

        // Display results
        displayResults(results);
    }

    // Function to display results
    function displayResults(results) {
        if (results.length === 0) {
            resultsContent.innerHTML = '<p>Please enter two values to calculate the others.</p>';
            return;
        }

        let html = '<ul>';
        results.forEach(result => {
            html += `<li>${result}</li>`;
        });
        html += '</ul>';
        resultsContent.innerHTML = html;
    }

    // Function to reset calculator
    function reset() {
        voltageInput.value = '';
        currentInput.value = '';
        resistanceInput.value = '';
        powerInput.value = '';
        voltageUnit.value = 'V';
        currentUnit.value = 'A';
        resistanceUnit.value = 'Ω';
        powerUnit.value = 'W';
        resultsContent.innerHTML = '';
    }

    // Add input validation on keypress
    [voltageInput, currentInput, resistanceInput, powerInput].forEach(input => {
        input.addEventListener('keypress', (e) => {
            // Allow only numbers, decimal point, and control characters
            if (!/[\d.]/.test(e.key) && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
            }
        });
    });
})();
</script> 