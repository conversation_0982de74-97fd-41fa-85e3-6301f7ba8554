<!-- Engineering Tools Homepage for WordPress -->
<!--
========================================
TOOL ENABLE/DISABLE MANAGEMENT
========================================
To enable/disable tools, find the tool card below and change data-enabled attribute:
- data-enabled="true"  = Tool is ENABLED and visible
- data-enabled="false" = Tool is DISABLED and hidden

Currently ENABLED tools:
✅ Ohm's Law Calculator
✅ Voltage Divider Calculator
✅ Resistor Color Code Calculator
✅ Wire Gauge Calculator
✅ Power Calculator
✅ LED Resistor Calculator
✅ 555 Timer IC Calculator
✅ Capacitor Calculator
✅ Inductor Calculator
✅ Transformer Calculator
✅ Unit Converter

Currently DISABLED tools:
❌ Beam Deflection Calculator
❌ Gear Ratio Calculator
❌ Spring Calculator
❌ Concrete Mix Calculator
❌ Heat Transfer Calculator
========================================
-->
<style>
    /* Reset and base styles */
    .engineering-tools * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .engineering-tools {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.6;
        color: #2c3e50;
        background: #fff;
        padding: 30px;
        max-width: 1200px;
        margin: 0 auto;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    }

    /* Header styles */
    .engineering-tools header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 25px;
        border-bottom: 2px solid #e1e8ed;
    }

    .engineering-tools h1 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .engineering-tools .intro {
        font-size: 1.1rem;
        color: #5a6c7d;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.7;
    }

    /* Search section */
    .engineering-tools .search-section {
        background: #f7fafc;
        padding: 30px 25px;
        border-radius: 12px;
        margin-bottom: 40px;
        text-align: center;
        border: 1px solid #e1e8ed;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .engineering-tools .search-section h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.6rem;
        font-weight: 600;
    }

    .engineering-tools .search-container {
        position: relative;
        max-width: 500px;
        margin: 0 auto;
    }

    .engineering-tools .search-input {
        width: 100%;
        padding: 15px 20px 15px 50px;
        border: 1px solid #e1e8ed;
        border-radius: 30px;
        font-size: 1.1rem;
        background: #fff;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .engineering-tools .search-input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
    }

    .engineering-tools .search-icon {
        position: absolute;
        left: 18px;
        top: 50%;
        transform: translateY(-50%);
        color: #5a6c7d;
        font-size: 1.2rem;
    }

    /* Categories section */
    .engineering-tools .categories-section {
        margin-bottom: 30px;
    }

    .engineering-tools .categories-section h2 {
        color: #2c3e50;
        margin-bottom: 25px;
        font-size: 2rem;
        text-align: center;
        font-weight: 700;
    }

    .engineering-tools .category-filters {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 30px;
    }

    .engineering-tools .category-btn {
        padding: 12px 20px;
        border: 1px solid #3498db;
        background: #fff;
        color: #3498db;
        border-radius: 25px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .engineering-tools .category-btn:hover,
    .engineering-tools .category-btn.active {
        background: #3498db;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
    }

    /* Tools grid */
    .engineering-tools .tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .engineering-tools .tool-card {
        background: #fff;
        border: 1px solid #e1e8ed;
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .engineering-tools .tool-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        border-color: #3498db;
    }

    .engineering-tools .tool-card.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f8f9fa;
    }

    .engineering-tools .tool-card.disabled:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border-color: #e1e8ed;
    }

    .engineering-tools .tool-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #3498db;
        display: block;
    }

    .engineering-tools .tool-card h3 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .engineering-tools .tool-card p {
        color: #5a6c7d;
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 15px;
    }

    .engineering-tools .tool-category {
        display: inline-block;
        background: #007cba;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 500;
    }



    /* Footer section */
    .engineering-tools .footer-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        margin-top: 30px;
        border: 1px solid #e9ecef;
    }

    .engineering-tools .footer-section h2 {
        margin-bottom: 10px;
        font-size: 1.3rem;
        color: #333;
    }

    .engineering-tools .footer-section p {
        font-size: 0.95rem;
        color: #666;
        max-width: 500px;
        margin: 0 auto;
    }

    /* No results message */
    .engineering-tools .no-results {
        text-align: center;
        padding: 40px 20px;
        color: #666;
    }

    .engineering-tools .no-results h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
        color: #333;
    }

    /* Hidden class for filtering */
    .engineering-tools .tool-card.hidden {
        display: none;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .engineering-tools {
            padding: 15px;
        }

        .engineering-tools h1 {
            font-size: 1.8rem;
        }

        .engineering-tools .search-section,
        .engineering-tools .footer-section {
            padding: 20px 15px;
        }

        .engineering-tools .tools-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .engineering-tools .tool-card {
            padding: 15px;
        }

        .engineering-tools .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .engineering-tools {
            padding: 12px;
        }

        .engineering-tools h1 {
            font-size: 1.6rem;
        }

        .engineering-tools .intro {
            font-size: 0.95rem;
        }

        .engineering-tools .search-section,
        .engineering-tools .footer-section {
            padding: 18px 12px;
        }

        .engineering-tools .tools-grid {
            gap: 12px;
        }

        .engineering-tools .tool-card {
            padding: 15px;
        }

        .engineering-tools .tool-card h3 {
            font-size: 1.1rem;
        }

        .engineering-tools .tool-card p {
            font-size: 0.9rem;
        }

        .engineering-tools .category-filters {
            gap: 8px;
        }

        .engineering-tools .category-btn {
            padding: 8px 12px;
            font-size: 0.8rem;
        }

        .engineering-tools .stats-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .engineering-tools .search-input {
            padding: 10px 12px 10px 40px;
            font-size: 0.95rem;
        }

        .engineering-tools .search-icon {
            left: 15px;
            font-size: 1rem;
        }
    }
</style>

<div class="engineering-tools">
    <header>
        <h1>Engineering Calculator Tools</h1>
        <p class="intro">Professional engineering calculators and tools for students, engineers, and professionals. Accurate, easy-to-use, and completely free educational resources.</p>
    </header>

    <div class="search-section">
        <h2>Find Engineering Tools</h2>
        <div class="search-container">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" id="toolSearch" placeholder="Search calculators and tools...">
        </div>
    </div>

    <div class="categories-section">
        <h2>Browse by Category</h2>
        <div class="category-filters">
            <button class="category-btn active" data-category="all">All Tools</button>
            <button class="category-btn" data-category="electrical">Electrical</button>
            <button class="category-btn" data-category="mechanical">Mechanical</button>
            <button class="category-btn" data-category="civil">Civil</button>
            <button class="category-btn" data-category="electronics">Electronics</button>
            <button class="category-btn" data-category="conversion">Conversion</button>
        </div>
    </div>

    <div class="tools-grid" id="toolsGrid">
        <!-- ENABLE/DISABLE TOOLS: Set data-enabled="true" to enable, "false" to disable -->

        <!-- Electrical & Electronics Tools -->
        <div class="tool-card" data-category="electrical electronics" data-keywords="ohm law voltage current resistance" data-enabled="true" data-url="voltage-divider-calculator.html">
            <span class="tool-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                </svg>
            </span>
            <h3>Ohm's Law Calculator</h3>
            <p>Calculate voltage, current, resistance, and power using Ohm's Law. Essential for electrical circuit analysis.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="voltage divider resistor circuit" data-enabled="true" data-url="voltage-divider-calculator.html">
            <span class="tool-icon">�</span>
            <h3>Voltage Divider Calculator</h3>
            <p>Calculate output voltage and resistor values for voltage divider circuits. Perfect for sensor interfacing.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="resistor color code bands" data-enabled="true" data-url="resistor-color-calculator.html">
            <span class="tool-icon">🎨</span>
            <h3>Resistor Color Code Calculator</h3>
            <p>Decode resistor values from color bands. Supports 4, 5, and 6 band resistors with tolerance calculation.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electrical" data-keywords="wire gauge awg current capacity" data-enabled="true" data-url="wire-gauge-calculator.html">
            <span class="tool-icon">🔗</span>
            <h3>Wire Gauge Calculator</h3>
            <p>Calculate appropriate wire gauge for electrical installations. Includes voltage drop and current capacity.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="power watts voltage current" data-enabled="true" data-url="power-calculator.html">
            <span class="tool-icon">�</span>
            <h3>Power Calculator</h3>
            <p>Calculate electrical power, energy consumption, and efficiency. Essential for power system analysis.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electronics" data-keywords="led resistor current limiting" data-enabled="true" data-url="led-resistor-calculator.html">
            <span class="tool-icon">💡</span>
            <h3>LED Resistor Calculator</h3>
            <p>Calculate current limiting resistor values for LED circuits. Supports single and multiple LED configurations.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electronics" data-keywords="555 timer ic frequency duty cycle" data-enabled="true" data-url="555-ic-calculator.html">
            <span class="tool-icon">🔲</span>
            <h3>555 Timer IC Calculator</h3>
            <p>Calculate timing components for 555 timer circuits. Supports astable and monostable configurations.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="capacitor reactance impedance frequency" data-enabled="true" data-url="capacitor-calculator.html">
            <span class="tool-icon">⚡</span>
            <h3>Capacitor Calculator</h3>
            <p>Calculate capacitive reactance, impedance, and energy storage. Essential for AC circuit design.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="inductor reactance impedance frequency" data-enabled="true" data-url="inductor-calculator.html">
            <span class="tool-icon">🌀</span>
            <h3>Inductor Calculator</h3>
            <p>Calculate inductive reactance, impedance, and energy storage. Essential for AC circuit analysis.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical" data-keywords="transformer turns ratio voltage current" data-enabled="true" data-url="transformer-calculator.html">
            <span class="tool-icon">🔄</span>
            <h3>Transformer Calculator</h3>
            <p>Calculate transformer turns ratio, voltage, current, and power relationships for AC circuits.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <!-- Mechanical Engineering Tools -->
        <div class="tool-card" data-category="mechanical" data-keywords="beam deflection moment stress" data-enabled="false" data-url="beam-deflection-calculator.html">
            <span class="tool-icon">🏗️</span>
            <h3>Beam Deflection Calculator</h3>
            <p>Calculate beam deflection, bending moment, and stress for various loading conditions and supports.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="gear ratio speed torque" data-enabled="false" data-url="gear-ratio-calculator.html">
            <span class="tool-icon">⚙️</span>
            <h3>Gear Ratio Calculator</h3>
            <p>Calculate gear ratios, speed reduction, torque multiplication for mechanical drive systems.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="spring constant force displacement" data-enabled="false" data-url="spring-calculator.html">
            <span class="tool-icon">🌸</span>
            <h3>Spring Calculator</h3>
            <p>Calculate spring constant, force, displacement, and energy for compression and extension springs.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="heat transfer conduction convection" data-enabled="false" data-url="heat-transfer-calculator.html">
            <span class="tool-icon">🌡️</span>
            <h3>Heat Transfer Calculator</h3>
            <p>Calculate heat transfer rates for conduction, convection, and radiation. Thermal analysis tool.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <!-- Civil Engineering Tools -->
        <div class="tool-card" data-category="civil" data-keywords="concrete mix design cement aggregate" data-enabled="false" data-url="concrete-mix-calculator.html">
            <span class="tool-icon">🏢</span>
            <h3>Concrete Mix Calculator</h3>
            <p>Calculate concrete mix proportions for different grades and applications. Includes material quantities.</p>
            <span class="tool-category">Civil</span>
        </div>

        <!-- Conversion Tools -->
        <div class="tool-card" data-category="conversion" data-keywords="unit converter metric imperial" data-enabled="true" data-url="unit-converter.html">
            <span class="tool-icon">�</span>
            <h3>Unit Converter</h3>
            <p>Convert between different units of measurement. Supports length, weight, temperature, and more.</p>
            <span class="tool-category">Conversion</span>
        </div>
    </div>


</div>

<script>
(function() {
    // Get DOM elements
    const searchInput = document.getElementById('toolSearch');
    const categoryButtons = document.querySelectorAll('.category-btn');
    const toolCards = document.querySelectorAll('.tool-card');
    const toolsGrid = document.getElementById('toolsGrid');

    let currentCategory = 'all';
    let currentSearchTerm = '';

    // Search functionality
    searchInput.addEventListener('input', function() {
        currentSearchTerm = this.value.toLowerCase().trim();
        filterTools();
    });

    // Category filtering
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            categoryButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Update current category
            currentCategory = this.dataset.category;

            // Filter tools
            filterTools();
        });
    });

    // Filter tools based on category and search term
    function filterTools() {
        let visibleCount = 0;

        toolCards.forEach(card => {
            const categories = card.dataset.category || '';
            const keywords = card.dataset.keywords || '';
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();

            // Check category match
            const categoryMatch = currentCategory === 'all' ||
                                categories.includes(currentCategory);

            // Check search term match
            const searchMatch = currentSearchTerm === '' ||
                               title.includes(currentSearchTerm) ||
                               description.includes(currentSearchTerm) ||
                               keywords.includes(currentSearchTerm) ||
                               categories.includes(currentSearchTerm);

            // Show/hide card based on matches
            if (categoryMatch && searchMatch) {
                card.classList.remove('hidden');
                visibleCount++;
            } else {
                card.classList.add('hidden');
            }
        });

        // Show/hide no results message
        showNoResultsMessage(visibleCount === 0);
    }

    // Show or hide no results message
    function showNoResultsMessage(show) {
        let noResultsDiv = document.querySelector('.no-results');

        if (show && !noResultsDiv) {
            // Create no results message
            noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'no-results';
            noResultsDiv.innerHTML = `
                <h3>🔍 No Tools Found</h3>
                <p>No tools match your current search criteria.</p>
                <p>Try adjusting your search terms or selecting a different category.</p>
            `;
            toolsGrid.appendChild(noResultsDiv);
        } else if (!show && noResultsDiv) {
            // Remove no results message
            noResultsDiv.remove();
        }
    }

    // Add click handlers to tool cards with enable/disable functionality
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const isEnabled = this.getAttribute('data-enabled') === 'true';
            const toolUrl = this.getAttribute('data-url');
            const toolName = this.querySelector('h3').textContent;

            if (!isEnabled) {
                // Show message for disabled tools
                alert(`${toolName} is currently disabled. Please check back later!`);
                return;
            }

            // Add visual feedback for enabled tools
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Navigate to the tool if URL is provided
            if (toolUrl) {
                console.log(`Navigating to: ${toolUrl}`);
                // Uncomment the line below to enable actual navigation
                // window.location.href = toolUrl;
            } else {
                console.log(`Clicked on: ${toolName} (no URL specified)`);
            }
        });
    });

    // Initialize disabled tool styling
    function initializeToolStates() {
        toolCards.forEach(card => {
            const isEnabled = card.getAttribute('data-enabled') === 'true';
            if (!isEnabled) {
                card.classList.add('disabled');
            }
        });
    }

    // Call initialization
    initializeToolStates();

    // Add keyboard navigation for search
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            currentSearchTerm = '';
            filterTools();
            this.blur();
        }
    });

    // Add smooth scrolling for better UX
    function smoothScrollToTools() {
        const toolsSection = document.querySelector('.tools-grid');
        toolsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Trigger smooth scroll when search is focused
    searchInput.addEventListener('focus', function() {
        setTimeout(smoothScrollToTools, 300);
    });

    // Initialize with animation
    function initializeWithAnimation() {
        toolCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Run initialization when page loads
    document.addEventListener('DOMContentLoaded', initializeWithAnimation);

    // Add search suggestions (simple implementation)
    const searchSuggestions = [
        'ohm law', 'voltage', 'current', 'resistance', 'power',
        'led resistor', 'color code', '555 timer', 'voltage divider',
        'capacitor', 'inductor', 'transformer', 'beam deflection',
        'gear ratio', 'spring', 'concrete', 'heat transfer', 'unit converter'
    ];

    // Simple autocomplete functionality
    searchInput.addEventListener('input', function() {
        // This is a basic implementation - in production you might want
        // a more sophisticated autocomplete with dropdown suggestions
        const value = this.value.toLowerCase();
        if (value.length > 2) {
            const matches = searchSuggestions.filter(suggestion =>
                suggestion.includes(value)
            );

            // You could display these matches in a dropdown
            console.log('Search suggestions:', matches);
        }
    });
})();
</script>