<!-- Engineering Tools Homepage for WordPress -->
<style>
    /* Reset and base styles */
    .engineering-tools * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .engineering-tools {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #fff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Header styles */
    .engineering-tools header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .engineering-tools h1 {
        color: #1a1a1a;
        margin-bottom: 10px;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .engineering-tools .intro {
        font-size: 1rem;
        color: #555;
        max-width: 700px;
        margin: 0 auto;
    }

    /* Search section */
    .engineering-tools .search-section {
        background: #f8f9fa;
        padding: 25px 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        text-align: center;
        border: 1px solid #e9ecef;
    }

    .engineering-tools .search-section h2 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.5rem;
        font-weight: 500;
    }

    .engineering-tools .search-container {
        position: relative;
        max-width: 500px;
        margin: 0 auto;
    }

    .engineering-tools .search-input {
        width: 100%;
        padding: 12px 15px 12px 40px;
        border: 1px solid #ddd;
        border-radius: 25px;
        font-size: 1rem;
        background: #fff;
        transition: border-color 0.2s;
    }

    .engineering-tools .search-input:focus {
        outline: none;
        border-color: #007cba;
    }

    .engineering-tools .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 1rem;
    }

    /* Categories section */
    .engineering-tools .categories-section {
        margin-bottom: 30px;
    }

    .engineering-tools .categories-section h2 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
        font-weight: 500;
    }

    .engineering-tools .category-filters {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 25px;
    }

    .engineering-tools .category-btn {
        padding: 8px 16px;
        border: 1px solid #007cba;
        background: #fff;
        color: #007cba;
        border-radius: 20px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .engineering-tools .category-btn:hover,
    .engineering-tools .category-btn.active {
        background: #007cba;
        color: #fff;
    }

    /* Tools grid */
    .engineering-tools .tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .engineering-tools .tool-card {
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: all 0.2s;
        cursor: pointer;
    }

    .engineering-tools .tool-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #007cba;
    }

    .engineering-tools .tool-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
        color: #007cba;
        display: block;
    }

    .engineering-tools .tool-card h3 {
        color: #333;
        margin-bottom: 8px;
        font-size: 1.2rem;
        font-weight: 500;
    }

    .engineering-tools .tool-card p {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .engineering-tools .tool-category {
        display: inline-block;
        background: #007cba;
        color: #fff;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Stats section */
    .engineering-tools .stats-section {
        background: #f8f9fa;
        padding: 25px 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        text-align: center;
        border: 1px solid #e9ecef;
    }

    .engineering-tools .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 15px;
    }

    .engineering-tools .stat-item {
        text-align: center;
    }

    .engineering-tools .stat-number {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #007cba;
    }

    .engineering-tools .stat-label {
        font-size: 0.9rem;
        color: #666;
    }

    /* Footer section */
    .engineering-tools .footer-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        margin-top: 30px;
        border: 1px solid #e9ecef;
    }

    .engineering-tools .footer-section h2 {
        margin-bottom: 10px;
        font-size: 1.3rem;
        color: #333;
    }

    .engineering-tools .footer-section p {
        font-size: 0.95rem;
        color: #666;
        max-width: 500px;
        margin: 0 auto;
    }

    /* No results message */
    .engineering-tools .no-results {
        text-align: center;
        padding: 40px 20px;
        color: #666;
    }

    .engineering-tools .no-results h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
        color: #333;
    }

    /* Hidden class for filtering */
    .engineering-tools .tool-card.hidden {
        display: none;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .engineering-tools {
            padding: 15px;
        }

        .engineering-tools h1 {
            font-size: 1.8rem;
        }

        .engineering-tools .search-section,
        .engineering-tools .stats-section,
        .engineering-tools .footer-section {
            padding: 20px 15px;
        }

        .engineering-tools .tools-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .engineering-tools .tool-card {
            padding: 15px;
        }

        .engineering-tools .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .engineering-tools .category-filters {
            gap: 8px;
        }

        .engineering-tools .category-btn {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .engineering-tools .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="engineering-tools">
    <header>
        <h1>Engineering Calculator Tools</h1>
        <p class="intro">Professional engineering calculators and tools for students, engineers, and professionals. Accurate, easy-to-use, and completely free educational resources.</p>
    </header>

    <div class="search-section">
        <h2>Find Engineering Tools</h2>
        <div class="search-container">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" id="toolSearch" placeholder="Search calculators and tools...">
        </div>
    </div>

    <div class="categories-section">
        <h2>Browse by Category</h2>
        <div class="category-filters">
            <button class="category-btn active" data-category="all">All Tools</button>
            <button class="category-btn" data-category="electrical">Electrical</button>
            <button class="category-btn" data-category="mechanical">Mechanical</button>
            <button class="category-btn" data-category="civil">Civil</button>
            <button class="category-btn" data-category="electronics">Electronics</button>
            <button class="category-btn" data-category="conversion">Conversion</button>
        </div>
    </div>

    <div class="tools-grid" id="toolsGrid">
        <!-- Existing Tools -->
        <div class="tool-card" data-category="electrical electronics" data-keywords="ohm law voltage current resistance">
            <span class="tool-icon">⚡</span>
            <h3>Ohm's Law Calculator</h3>
            <p>Calculate voltage, current, resistance, and power using Ohm's Law. Essential for electrical circuit analysis.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electronics electrical" data-keywords="led resistor current limiting">
            <span class="tool-icon">💡</span>
            <h3>LED Resistor Calculator</h3>
            <p>Calculate the correct current limiting resistor for LEDs. Supports single and multiple LED configurations.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electronics electrical" data-keywords="resistor color code bands">
            <span class="tool-icon">🎨</span>
            <h3>Resistor Color Code</h3>
            <p>Decode resistor values from color bands. Interactive visual resistor with 4, 5, and 6 band support.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electronics electrical" data-keywords="555 timer ic astable monostable">
            <span class="tool-icon">⏱️</span>
            <h3>555 Timer Calculator</h3>
            <p>Design 555 timer circuits for astable and monostable modes. Calculate frequency, duty cycle, and timing.</p>
            <span class="tool-category">Electronics</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="voltage divider resistor ratio">
            <span class="tool-icon">📊</span>
            <h3>Voltage Divider Calculator</h3>
            <p>Calculate voltage division ratios and resistor values. Essential for reference voltage generation.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="power watt voltage current">
            <span class="tool-icon">⚡</span>
            <h3>Power Calculator</h3>
            <p>Calculate electrical power, voltage, current, and resistance using various power formulas.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <!-- New Tools -->
        <div class="tool-card" data-category="electrical electronics" data-keywords="capacitor reactance impedance frequency">
            <span class="tool-icon">🔋</span>
            <h3>Capacitor Calculator</h3>
            <p>Calculate capacitive reactance, impedance, and energy storage. Includes parallel/series combinations.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical electronics" data-keywords="inductor reactance impedance frequency">
            <span class="tool-icon">🌀</span>
            <h3>Inductor Calculator</h3>
            <p>Calculate inductive reactance, impedance, and energy storage. Essential for AC circuit analysis.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="electrical" data-keywords="transformer turns ratio voltage current">
            <span class="tool-icon">🔄</span>
            <h3>Transformer Calculator</h3>
            <p>Calculate transformer turns ratio, voltage, current, and power relationships for AC circuits.</p>
            <span class="tool-category">Electrical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="beam deflection moment stress">
            <span class="tool-icon">🏗️</span>
            <h3>Beam Deflection Calculator</h3>
            <p>Calculate beam deflection, bending moment, and stress for various loading conditions and supports.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="gear ratio speed torque">
            <span class="tool-icon">⚙️</span>
            <h3>Gear Ratio Calculator</h3>
            <p>Calculate gear ratios, speed reduction, torque multiplication for mechanical drive systems.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="spring constant force displacement">
            <span class="tool-icon">🌸</span>
            <h3>Spring Calculator</h3>
            <p>Calculate spring constant, force, displacement, and energy for compression and extension springs.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="civil" data-keywords="concrete mix design cement aggregate">
            <span class="tool-icon">🏢</span>
            <h3>Concrete Mix Calculator</h3>
            <p>Calculate concrete mix proportions for different grades and applications. Includes material quantities.</p>
            <span class="tool-category">Civil</span>
        </div>

        <div class="tool-card" data-category="mechanical" data-keywords="heat transfer conduction convection">
            <span class="tool-icon">🌡️</span>
            <h3>Heat Transfer Calculator</h3>
            <p>Calculate heat transfer rates for conduction, convection, and radiation. Thermal analysis tool.</p>
            <span class="tool-category">Mechanical</span>
        </div>

        <div class="tool-card" data-category="conversion" data-keywords="unit converter length mass temperature">
            <span class="tool-icon">🔄</span>
            <h3>Unit Converter</h3>
            <p>Convert between various units of measurement. Length, mass, temperature, pressure, and more.</p>
            <span class="tool-category">Conversion</span>
        </div>

        <div class="tool-card" data-category="electrical" data-keywords="wire gauge current capacity ampacity">
            <span class="tool-icon">🔌</span>
            <h3>Wire Gauge Calculator</h3>
            <p>Calculate wire gauge requirements based on current, voltage drop, and distance. Safety compliance.</p>
            <span class="tool-category">Electrical</span>
        </div>
    </div>

    <div class="stats-section">
        <h2>📈 Tools Statistics</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">16</div>
                <div class="stat-label">Total Tools</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">Free to Use</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Available</div>
            </div>
        </div>
    </div>

    <div class="footer-section">
        <h2>More Engineering Tools Coming Soon</h2>
        <p>We regularly add new engineering calculators and tools. All tools are designed for accuracy, ease of use, and educational value. Perfect for students, engineers, and professionals.</p>
    </div>
</div>

<script>
(function() {
    // Get DOM elements
    const searchInput = document.getElementById('toolSearch');
    const categoryButtons = document.querySelectorAll('.category-btn');
    const toolCards = document.querySelectorAll('.tool-card');
    const toolsGrid = document.getElementById('toolsGrid');

    let currentCategory = 'all';
    let currentSearchTerm = '';

    // Search functionality
    searchInput.addEventListener('input', function() {
        currentSearchTerm = this.value.toLowerCase().trim();
        filterTools();
    });

    // Category filtering
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            categoryButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Update current category
            currentCategory = this.dataset.category;

            // Filter tools
            filterTools();
        });
    });

    // Filter tools based on category and search term
    function filterTools() {
        let visibleCount = 0;

        toolCards.forEach(card => {
            const categories = card.dataset.category || '';
            const keywords = card.dataset.keywords || '';
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();

            // Check category match
            const categoryMatch = currentCategory === 'all' ||
                                categories.includes(currentCategory);

            // Check search term match
            const searchMatch = currentSearchTerm === '' ||
                               title.includes(currentSearchTerm) ||
                               description.includes(currentSearchTerm) ||
                               keywords.includes(currentSearchTerm) ||
                               categories.includes(currentSearchTerm);

            // Show/hide card based on matches
            if (categoryMatch && searchMatch) {
                card.classList.remove('hidden');
                visibleCount++;
            } else {
                card.classList.add('hidden');
            }
        });

        // Show/hide no results message
        showNoResultsMessage(visibleCount === 0);
    }

    // Show or hide no results message
    function showNoResultsMessage(show) {
        let noResultsDiv = document.querySelector('.no-results');

        if (show && !noResultsDiv) {
            // Create no results message
            noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'no-results';
            noResultsDiv.innerHTML = `
                <h3>🔍 No Tools Found</h3>
                <p>No tools match your current search criteria.</p>
                <p>Try adjusting your search terms or selecting a different category.</p>
            `;
            toolsGrid.appendChild(noResultsDiv);
        } else if (!show && noResultsDiv) {
            // Remove no results message
            noResultsDiv.remove();
        }
    }

    // Add click handlers to tool cards (for future navigation)
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const toolName = this.querySelector('h3').textContent;

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Here you would typically navigate to the specific tool
            // For now, we'll just show an alert
            console.log(`Clicked on: ${toolName}`);

            // In a real implementation, you might do:
            // window.location.href = `${toolName.toLowerCase().replace(/\s+/g, '-')}-calculator.html`;
        });
    });

    // Add keyboard navigation for search
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            currentSearchTerm = '';
            filterTools();
            this.blur();
        }
    });

    // Add smooth scrolling for better UX
    function smoothScrollToTools() {
        const toolsSection = document.querySelector('.tools-grid');
        toolsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Trigger smooth scroll when search is focused
    searchInput.addEventListener('focus', function() {
        setTimeout(smoothScrollToTools, 300);
    });

    // Initialize with animation
    function initializeWithAnimation() {
        toolCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Run initialization when page loads
    document.addEventListener('DOMContentLoaded', initializeWithAnimation);

    // Add search suggestions (simple implementation)
    const searchSuggestions = [
        'ohm law', 'voltage', 'current', 'resistance', 'power',
        'led resistor', 'color code', '555 timer', 'voltage divider',
        'capacitor', 'inductor', 'transformer', 'beam deflection',
        'gear ratio', 'spring', 'concrete', 'heat transfer', 'unit converter'
    ];

    // Simple autocomplete functionality
    searchInput.addEventListener('input', function() {
        // This is a basic implementation - in production you might want
        // a more sophisticated autocomplete with dropdown suggestions
        const value = this.value.toLowerCase();
        if (value.length > 2) {
            const matches = searchSuggestions.filter(suggestion =>
                suggestion.includes(value)
            );

            // You could display these matches in a dropdown
            console.log('Search suggestions:', matches);
        }
    });
})();
</script>