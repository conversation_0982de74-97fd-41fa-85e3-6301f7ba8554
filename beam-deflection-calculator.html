<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beam Deflection Calculator - SKR Electronics Lab</title>
    <meta name="description" content="Free beam deflection calculator for simply supported, cantilever, and fixed beams. Calculate maximum deflection, bending moment, and shear force.">
    <style>
        .beam-calc * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .beam-calc {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .beam-calc header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .beam-calc h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .beam-calc .intro {
            font-size: 1rem;
            color: #555;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .beam-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .beam-type-btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #fff;
            color: #007cba;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .beam-type-btn.active {
            background: #007cba;
            color: #fff;
        }
        
        .calculator {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .beam-diagram {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        
        .beam-visual {
            width: 100%;
            max-width: 400px;
            height: 100px;
            margin: 0 auto;
            position: relative;
            background: #f0f0f0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .input-field {
            display: flex;
            flex-direction: column;
        }
        
        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .input-field input, .input-field select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .input-field input:focus, .input-field select:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #007cba;
            background: #007cba;
            color: #fff;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #fff;
            color: #007cba;
        }
        
        .btn-secondary:hover {
            background: #007cba;
            color: #fff;
        }
        
        .results {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-top: 20px;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1rem;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .content-section p {
            margin-bottom: 12px;
            color: #555;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #e7f3ff;
            border-left: 3px solid #007cba;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .formula-box {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .beam-calc {
                padding: 15px;
            }
            
            .beam-calc h1 {
                font-size: 1.7rem;
            }
            
            .input-group {
                grid-template-columns: 1fr;
            }
            
            .beam-types {
                gap: 5px;
            }
            
            .beam-type-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="beam-calc">
        <header>
            <h1>Beam Deflection Calculator</h1>
            <p class="intro">Calculate beam deflection, bending moment, and shear force for different beam configurations and loading conditions. Essential tool for structural engineering.</p>
        </header>

        <div class="beam-types">
            <button class="beam-type-btn active" onclick="selectBeamType('simply-supported')">Simply Supported</button>
            <button class="beam-type-btn" onclick="selectBeamType('cantilever')">Cantilever</button>
            <button class="beam-type-btn" onclick="selectBeamType('fixed-fixed')">Fixed-Fixed</button>
        </div>

        <div class="beam-diagram">
            <h3 id="beam-title">Simply Supported Beam - Point Load at Center</h3>
            <div class="beam-visual" id="beam-visual">
                <div style="width: 80%; height: 8px; background: #8B4513; position: relative;">
                    <div style="position: absolute; bottom: -15px; left: 0; width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-bottom: 15px solid #333;"></div>
                    <div style="position: absolute; bottom: -15px; right: 0; width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-bottom: 15px solid #333;"></div>
                    <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); color: red; font-weight: bold;">P</div>
                </div>
            </div>
        </div>

        <div class="calculator">
            <div class="input-group">
                <div class="input-field">
                    <label for="load">Load (P) - N or kN</label>
                    <input type="number" id="load" step="any" placeholder="Enter load">
                </div>
                <div class="input-field">
                    <label for="length">Beam Length (L) - m</label>
                    <input type="number" id="length" step="any" placeholder="Enter length">
                </div>
                <div class="input-field">
                    <label for="elasticModulus">Elastic Modulus (E) - GPa</label>
                    <input type="number" id="elasticModulus" step="any" value="200" placeholder="Enter E">
                </div>
            </div>
            
            <div class="input-group">
                <div class="input-field">
                    <label for="momentOfInertia">Moment of Inertia (I) - m⁴</label>
                    <input type="number" id="momentOfInertia" step="any" placeholder="Enter I">
                </div>
                <div class="input-field">
                    <label for="loadPosition">Load Position (a/L)</label>
                    <input type="number" id="loadPosition" step="any" value="0.5" min="0" max="1" placeholder="0.5 for center">
                </div>
                <div class="input-field">
                    <label for="crossSection">Cross Section</label>
                    <select id="crossSection" onchange="updateMomentOfInertia()">
                        <option value="custom">Custom</option>
                        <option value="rectangular">Rectangular</option>
                        <option value="circular">Circular</option>
                        <option value="i-beam">I-Beam</option>
                    </select>
                </div>
            </div>
            
            <div id="sectionDimensions" style="display: none;">
                <div class="input-group">
                    <div class="input-field">
                        <label for="width">Width/Diameter (mm)</label>
                        <input type="number" id="width" step="any" placeholder="Enter width">
                    </div>
                    <div class="input-field">
                        <label for="height">Height (mm)</label>
                        <input type="number" id="height" step="any" placeholder="Enter height">
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="calculate()">Calculate</button>
                <button class="btn btn-secondary" onclick="reset()">Reset</button>
            </div>

            <div class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="content-section">
            <h2>Beam Deflection Formulas</h2>
            <p>The calculator uses standard beam deflection formulas based on beam configuration and loading conditions:</p>
            
            <div class="highlight-box">
                <h3>Simply Supported Beam - Center Load</h3>
                <div class="formula-box">
                    Maximum Deflection: δ = PL³/(48EI)<br>
                    Maximum Moment: M = PL/4<br>
                    Maximum Shear: V = P/2
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Cantilever Beam - End Load</h3>
                <div class="formula-box">
                    Maximum Deflection: δ = PL³/(3EI)<br>
                    Maximum Moment: M = PL<br>
                    Maximum Shear: V = P
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Fixed-Fixed Beam - Center Load</h3>
                <div class="formula-box">
                    Maximum Deflection: δ = PL³/(192EI)<br>
                    Maximum Moment: M = PL/8<br>
                    Maximum Shear: V = P/2
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentBeamType = 'simply-supported';
        
        function selectBeamType(type) {
            currentBeamType = type;
            
            // Update active button
            document.querySelectorAll('.beam-type-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update beam title and diagram
            const titles = {
                'simply-supported': 'Simply Supported Beam - Point Load at Center',
                'cantilever': 'Cantilever Beam - Point Load at Free End',
                'fixed-fixed': 'Fixed-Fixed Beam - Point Load at Center'
            };
            
            document.getElementById('beam-title').textContent = titles[type];
            
            // Show/hide load position for simply supported beam
            const loadPosField = document.getElementById('loadPosition').parentElement;
            loadPosField.style.display = type === 'simply-supported' ? 'flex' : 'none';
        }
        
        function updateMomentOfInertia() {
            const section = document.getElementById('crossSection').value;
            const dimensionsDiv = document.getElementById('sectionDimensions');
            
            if (section === 'custom') {
                dimensionsDiv.style.display = 'none';
            } else {
                dimensionsDiv.style.display = 'block';
                
                // Update labels based on section type
                const widthLabel = document.querySelector('#width').previousElementSibling;
                const heightLabel = document.querySelector('#height').previousElementSibling;
                
                if (section === 'circular') {
                    widthLabel.textContent = 'Diameter (mm)';
                    heightLabel.textContent = 'Height (mm)';
                    document.getElementById('height').style.display = 'none';
                    heightLabel.style.display = 'none';
                } else {
                    widthLabel.textContent = 'Width (mm)';
                    heightLabel.textContent = 'Height (mm)';
                    document.getElementById('height').style.display = 'block';
                    heightLabel.style.display = 'block';
                }
            }
        }
        
        function calculateMomentOfInertia() {
            const section = document.getElementById('crossSection').value;
            const width = parseFloat(document.getElementById('width').value) / 1000; // Convert mm to m
            const height = parseFloat(document.getElementById('height').value) / 1000; // Convert mm to m
            
            if (section === 'rectangular' && width && height) {
                return (width * Math.pow(height, 3)) / 12;
            } else if (section === 'circular' && width) {
                const radius = width / 2;
                return (Math.PI * Math.pow(radius, 4)) / 4;
            } else if (section === 'i-beam' && width && height) {
                // Simplified I-beam calculation (assumes uniform thickness)
                const thickness = width * 0.1; // Assume 10% of width
                return (width * Math.pow(height, 3) - (width - 2 * thickness) * Math.pow(height - 2 * thickness, 3)) / 12;
            }
            
            return null;
        }
        
        function calculate() {
            const load = parseFloat(document.getElementById('load').value);
            const length = parseFloat(document.getElementById('length').value);
            const E = parseFloat(document.getElementById('elasticModulus').value) * 1e9; // Convert GPa to Pa
            let I = parseFloat(document.getElementById('momentOfInertia').value);
            const loadPos = parseFloat(document.getElementById('loadPosition').value) || 0.5;
            
            // Calculate I from dimensions if not provided
            if (!I) {
                I = calculateMomentOfInertia();
                if (I) {
                    document.getElementById('momentOfInertia').value = I.toExponential(6);
                }
            }
            
            if (!load || !length || !E || !I) {
                alert('Please enter all required values');
                return;
            }
            
            let deflection, moment, shear;
            
            switch (currentBeamType) {
                case 'simply-supported':
                    if (loadPos === 0.5) {
                        // Center load
                        deflection = (load * Math.pow(length, 3)) / (48 * E * I);
                        moment = (load * length) / 4;
                        shear = load / 2;
                    } else {
                        // Off-center load
                        const a = loadPos * length;
                        const b = length - a;
                        deflection = (load * a * b * (length * length - a * a - b * b)) / (6 * E * I * length);
                        moment = (load * a * b) / length;
                        shear = Math.max(load * b / length, load * a / length);
                    }
                    break;
                    
                case 'cantilever':
                    deflection = (load * Math.pow(length, 3)) / (3 * E * I);
                    moment = load * length;
                    shear = load;
                    break;
                    
                case 'fixed-fixed':
                    deflection = (load * Math.pow(length, 3)) / (192 * E * I);
                    moment = (load * length) / 8;
                    shear = load / 2;
                    break;
            }
            
            const results = [
                `<div class="result-item"><strong>Maximum Deflection:</strong> ${formatValue(deflection)} m</div>`,
                `<div class="result-item"><strong>Maximum Bending Moment:</strong> ${formatValue(moment)} N⋅m</div>`,
                `<div class="result-item"><strong>Maximum Shear Force:</strong> ${formatValue(shear)} N</div>`,
                `<div class="result-item"><strong>Deflection/Length Ratio:</strong> ${(deflection/length).toFixed(6)}</div>`
            ];
            
            document.getElementById('results-content').innerHTML = results.join('');
            document.getElementById('results').style.display = 'block';
        }
        
        function formatValue(value) {
            if (Math.abs(value) >= 1e6) return (value / 1e6).toFixed(3) + 'M';
            if (Math.abs(value) >= 1e3) return (value / 1e3).toFixed(3) + 'k';
            if (Math.abs(value) >= 1) return value.toFixed(6);
            if (Math.abs(value) >= 1e-3) return (value * 1e3).toFixed(3) + 'm';
            if (Math.abs(value) >= 1e-6) return (value * 1e6).toFixed(3) + 'μ';
            return (value * 1e9).toFixed(3) + 'n';
        }
        
        function reset() {
            document.getElementById('load').value = '';
            document.getElementById('length').value = '';
            document.getElementById('elasticModulus').value = '200';
            document.getElementById('momentOfInertia').value = '';
            document.getElementById('loadPosition').value = '0.5';
            document.getElementById('crossSection').selectedIndex = 0;
            document.getElementById('width').value = '';
            document.getElementById('height').value = '';
            document.getElementById('sectionDimensions').style.display = 'none';
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
